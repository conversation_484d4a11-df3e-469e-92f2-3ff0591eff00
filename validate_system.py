#!/usr/bin/env python3
"""
Laser Demonstration System - System Validation Script
Comprehensive testing of all system components
"""

import os
import sys
import json
import time
import requests
import threading
from pathlib import Path

def test_imports():
    """Test all required imports"""
    print("Testing imports...")
    
    modules = [
        'flask',
        'flask_socketio',
        'cv2',
        'pyzbar',
        'qrcode',
        'PIL',
        'requests',
        'dotenv',
        'pydantic',
        'websockets',
        'numpy'
    ]
    
    failed = []
    for module in modules:
        try:
            __import__(module)
            print(f"  ✅ {module}")
        except ImportError as e:
            print(f"  ❌ {module}: {e}")
            failed.append(module)
    
    return len(failed) == 0

def test_file_structure():
    """Test required file structure"""
    print("\nTesting file structure...")
    
    required_files = [
        'app.py',
        'config.py',
        'ezcad3_client.py',
        'scanner.py',
        'template_manager.py',
        'safety_controller.py',
        'requirements.txt',
        'README.md',
        'templates/index.html',
        'static/css/style.css',
        'static/js/app.js'
    ]
    
    required_dirs = [
        'templates',
        'static',
        'static/css',
        'static/js',
        'static/images'
    ]
    
    missing = []
    
    # Check files
    for file_path in required_files:
        if os.path.exists(file_path):
            print(f"  ✅ {file_path}")
        else:
            print(f"  ❌ {file_path}")
            missing.append(file_path)
    
    # Check directories
    for dir_path in required_dirs:
        if os.path.isdir(dir_path):
            print(f"  ✅ {dir_path}/")
        else:
            print(f"  ❌ {dir_path}/")
            missing.append(dir_path)
    
    return len(missing) == 0

def test_configuration():
    """Test configuration loading"""
    print("\nTesting configuration...")
    
    try:
        from config import config
        
        # Test config access
        print(f"  ✅ EZcad3 host: {config.ezcad3.host}")
        print(f"  ✅ EZcad3 port: {config.ezcad3.port}")
        print(f"  ✅ Web server port: {config.web_server.port}")
        
        # Test config dict conversion
        config_dict = config.to_dict()
        print(f"  ✅ Config dictionary: {len(config_dict)} sections")
        
        return True
        
    except Exception as e:
        print(f"  ❌ Configuration error: {e}")
        return False

def test_ezcad3_client():
    """Test EZcad3 client functionality"""
    print("\nTesting EZcad3 client...")
    
    try:
        from ezcad3_client import EZcad3Client, LaserStatus, LaserCommand
        
        # Test client creation
        client = EZcad3Client()
        print(f"  ✅ Client created: {client.host}:{client.port}")
        
        # Test status enum
        print(f"  ✅ Status enum: {len(LaserStatus)} states")
        
        # Test command enum
        print(f"  ✅ Command enum: {len(LaserCommand)} commands")
        
        return True
        
    except Exception as e:
        print(f"  ❌ EZcad3 client error: {e}")
        return False

def test_scanner():
    """Test scanner functionality"""
    print("\nTesting scanner...")
    
    try:
        from scanner import BarcodeScanner
        
        scanner = BarcodeScanner()
        print(f"  ✅ Scanner created")
        
        # Test parsing
        test_data = '{"name": "Test", "company": "Test Co"}'
        parsed = scanner.parse_scan_data(test_data)
        print(f"  ✅ JSON parsing: {parsed['name']}")
        
        # Test validation
        valid = scanner.validate_scan_data(test_data)
        print(f"  ✅ Data validation: {valid}")
        
        return True
        
    except Exception as e:
        print(f"  ❌ Scanner error: {e}")
        return False

def test_template_manager():
    """Test template manager"""
    print("\nTesting template manager...")
    
    try:
        from template_manager import TemplateManager
        
        manager = TemplateManager()
        manager.initialize()
        print(f"  ✅ Template manager initialized")
        
        # Test template loading
        templates = manager.get_available_templates()
        print(f"  ✅ Available templates: {len(templates)}")
        
        if templates:
            template = templates[0]
            print(f"  ✅ Sample template: {template['name']}")
        
        return True
        
    except Exception as e:
        print(f"  ❌ Template manager error: {e}")
        return False

def test_safety_controller():
    """Test safety controller"""
    print("\nTesting safety controller...")
    
    try:
        from safety_controller import SafetyController, AutoSequenceState
        from ezcad3_client import EZcad3Client
        
        client = EZcad3Client()
        controller = SafetyController(client)
        print(f"  ✅ Safety controller created")
        
        # Test status
        status = controller.get_safety_status()
        print(f"  ✅ Safety status: {len(status)} fields")
        
        # Test state enum
        print(f"  ✅ Auto states: {len(AutoSequenceState)} states")
        
        return True
        
    except Exception as e:
        print(f"  ❌ Safety controller error: {e}")
        return False

def test_web_server():
    """Test web server startup"""
    print("\nTesting web server...")
    
    try:
        # Import Flask app
        from app import app, socketio
        print(f"  ✅ Flask app imported")
        
        # Test app configuration
        print(f"  ✅ App secret key configured")
        
        # Test routes (basic check)
        with app.test_client() as client:
            response = client.get('/')
            print(f"  ✅ Main route accessible: {response.status_code}")
            
            response = client.get('/api/status')
            print(f"  ✅ API status route: {response.status_code}")
        
        return True
        
    except Exception as e:
        print(f"  ❌ Web server error: {e}")
        return False

def test_templates():
    """Test template files"""
    print("\nTesting templates...")
    
    template_files = [
        'templates/business_card.json',
        'templates/name_tag.json',
        'templates/keychain.json'
    ]
    
    valid_count = 0
    
    for template_file in template_files:
        try:
            if os.path.exists(template_file):
                with open(template_file, 'r') as f:
                    data = json.load(f)
                print(f"  ✅ {template_file}: {data['name']}")
                valid_count += 1
            else:
                print(f"  ⚠️  {template_file}: Not found")
        except Exception as e:
            print(f"  ❌ {template_file}: {e}")
    
    return valid_count > 0

def run_comprehensive_test():
    """Run all tests"""
    print("=" * 60)
    print("LASER DEMONSTRATION SYSTEM - VALIDATION")
    print("=" * 60)
    
    tests = [
        ("Imports", test_imports),
        ("File Structure", test_file_structure),
        ("Configuration", test_configuration),
        ("EZcad3 Client", test_ezcad3_client),
        ("Scanner", test_scanner),
        ("Template Manager", test_template_manager),
        ("Safety Controller", test_safety_controller),
        ("Web Server", test_web_server),
        ("Templates", test_templates)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n{'='*20} {test_name} {'='*20}")
        try:
            if test_func():
                passed += 1
                print(f"✅ {test_name}: PASSED")
            else:
                print(f"❌ {test_name}: FAILED")
        except Exception as e:
            print(f"❌ {test_name}: ERROR - {e}")
    
    print("\n" + "=" * 60)
    print(f"VALIDATION RESULTS: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 ALL TESTS PASSED! System is ready for use.")
        print("\nNext steps:")
        print("1. Configure EZcad3 connection in .env file")
        print("2. Start the system: python app.py")
        print("3. Access web interface: http://localhost:5000")
    else:
        print("⚠️  Some tests failed. Please resolve issues before deployment.")
        print("\nFor help:")
        print("1. Check README.md for setup instructions")
        print("2. Run: python start.py for guided setup")
        print("3. Check individual component documentation")
    
    print("=" * 60)
    
    return passed == total

if __name__ == "__main__":
    success = run_comprehensive_test()
    sys.exit(0 if success else 1)
