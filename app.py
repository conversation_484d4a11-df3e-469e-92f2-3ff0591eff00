"""
Laser Demonstration System - Main Flask Application
Web server for exhibition laser control interface
"""

from flask import Flask, render_template, request, jsonify, send_from_directory
from flask_socketio import <PERSON>cket<PERSON>, emit
import os
import json
import time
import threading
from datetime import datetime
from typing import Dict, Any

from config import config
from ezcad3_client import laser_client, LaserStatus
from scanner import BarcodeScanner
from template_manager import TemplateManager
from safety_controller import SafetyController

# Initialize Flask app
app = Flask(__name__)
app.config['SECRET_KEY'] = config.web_server.secret_key

# Initialize SocketIO for real-time communication
socketio = SocketIO(app, cors_allowed_origins="*")

# Initialize system components
scanner = BarcodeScanner()
template_manager = TemplateManager()
safety_controller = SafetyController(laser_client)

# Global state
system_state = {
    'laser_status': LaserStatus.DISCONNECTED.value,
    'current_template': None,
    'scan_data': None,
    'marking_progress': 0,
    'safety_door_closed': False,
    'emergency_stop': False,
    'auto_mode_active': False,
    'last_error': None
}

def update_system_state(key: str, value: Any):
    """Update system state and broadcast to clients"""
    system_state[key] = value
    socketio.emit('system_state_update', {key: value})

def laser_status_callback(status: LaserStatus, data: Dict):
    """Callback for laser status changes"""
    update_system_state('laser_status', status.value)
    if 'error' in data:
        update_system_state('last_error', data['error'])

# Register laser status callback
laser_client.add_status_callback(laser_status_callback)

# Register safety controller callbacks
def safety_progress_callback(progress: int, message: str):
    """Callback for safety controller progress updates"""
    update_system_state('marking_progress', progress)
    socketio.emit('progress_update', {
        'progress': progress,
        'message': message,
        'timestamp': time.time()
    })

def safety_state_callback(state, data: dict):
    """Callback for safety controller state changes"""
    socketio.emit('auto_state_update', {
        'state': state.value,
        'data': data,
        'timestamp': time.time()
    })

safety_controller.add_progress_callback(safety_progress_callback)
safety_controller.add_state_callback(safety_state_callback)

@app.route('/')
def index():
    """Main interface page"""
    return render_template('index.html', 
                         config=config.to_dict(),
                         system_state=system_state)

@app.route('/api/status')
def get_status():
    """Get current system status"""
    return jsonify({
        'success': True,
        'data': system_state,
        'timestamp': time.time()
    })

@app.route('/api/connect', methods=['POST'])
def connect_laser():
    """Connect to EZcad3 laser system"""
    try:
        response = laser_client.connect()
        return jsonify({
            'success': response.success,
            'message': response.message,
            'timestamp': time.time()
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'Connection error: {str(e)}',
            'timestamp': time.time()
        }), 500

@app.route('/api/disconnect', methods=['POST'])
def disconnect_laser():
    """Disconnect from EZcad3 laser system"""
    try:
        response = laser_client.disconnect()
        return jsonify({
            'success': response.success,
            'message': response.message,
            'timestamp': time.time()
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'Disconnect error: {str(e)}',
            'timestamp': time.time()
        }), 500

@app.route('/api/scan', methods=['POST'])
def process_scan():
    """Process barcode/QR code scan"""
    try:
        # Handle both JSON and form data
        if request.is_json:
            data = request.get_json()
            scan_method = data.get('method', 'manual')
            scan_data = data.get('data', '')
        else:
            # Handle file upload from camera
            scan_method = request.form.get('method', 'camera')
            if 'image' in request.files:
                # Save uploaded image temporarily and scan it
                image_file = request.files['image']
                temp_path = f"temp_scan_{int(time.time())}.jpg"
                image_file.save(temp_path)

                try:
                    scan_result = scanner.scan_from_image(temp_path)
                    os.remove(temp_path)  # Clean up temp file

                    if scan_result:
                        scan_data = scan_result
                    else:
                        return jsonify({
                            'success': False,
                            'message': 'No barcode/QR code detected in image',
                            'timestamp': time.time()
                        })
                except Exception as e:
                    if os.path.exists(temp_path):
                        os.remove(temp_path)
                    raise e
            else:
                return jsonify({
                    'success': False,
                    'message': 'No image provided',
                    'timestamp': time.time()
                }), 400

        if scan_method == 'camera' and not scan_data:
            # Use live camera scanner
            result = scanner.scan_from_camera()
            if result:
                scan_data = result
            else:
                return jsonify({
                    'success': False,
                    'message': 'No barcode/QR code detected',
                    'timestamp': time.time()
                })

        # Parse scan data
        parsed_data = scanner.parse_scan_data(scan_data)
        update_system_state('scan_data', parsed_data)

        return jsonify({
            'success': True,
            'message': 'Scan processed successfully',
            'data': parsed_data,
            'timestamp': time.time()
        })

    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'Scan error: {str(e)}',
            'timestamp': time.time()
        }), 500

@app.route('/api/templates')
def get_templates():
    """Get available templates"""
    try:
        templates = template_manager.get_available_templates()
        return jsonify({
            'success': True,
            'data': templates,
            'timestamp': time.time()
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'Template error: {str(e)}',
            'timestamp': time.time()
        }), 500

@app.route('/api/template/load', methods=['POST'])
def load_template():
    """Load template with variables"""
    try:
        data = request.get_json()
        template_name = data.get('template')
        variables = data.get('variables', {})
        
        # Load template in EZcad3
        response = laser_client.load_template(template_name)
        if not response.success:
            return jsonify({
                'success': False,
                'message': f'Failed to load template: {response.message}',
                'timestamp': time.time()
            })
        
        # Set variables
        for var_name, var_value in variables.items():
            var_response = laser_client.set_text_variable(var_name, str(var_value))
            if not var_response.success:
                return jsonify({
                    'success': False,
                    'message': f'Failed to set variable {var_name}: {var_response.message}',
                    'timestamp': time.time()
                })
        
        update_system_state('current_template', template_name)
        
        return jsonify({
            'success': True,
            'message': 'Template loaded successfully',
            'data': {'template': template_name, 'variables': variables},
            'timestamp': time.time()
        })
        
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'Template load error: {str(e)}',
            'timestamp': time.time()
        }), 500

@app.route('/api/laser/start', methods=['POST'])
def start_marking():
    """Start laser marking process"""
    try:
        if not laser_client.connected:
            return jsonify({
                'success': False,
                'message': 'Laser not connected',
                'timestamp': time.time()
            }), 400
        
        response = laser_client.start_marking()
        return jsonify({
            'success': response.success,
            'message': response.message,
            'timestamp': time.time()
        })
        
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'Start marking error: {str(e)}',
            'timestamp': time.time()
        }), 500

@app.route('/api/laser/stop', methods=['POST'])
def stop_marking():
    """Stop laser marking process"""
    try:
        response = laser_client.stop_marking()
        update_system_state('auto_mode_active', False)
        
        return jsonify({
            'success': response.success,
            'message': response.message,
            'timestamp': time.time()
        })
        
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'Stop marking error: {str(e)}',
            'timestamp': time.time()
        }), 500

@app.route('/api/laser/emergency_stop', methods=['POST'])
def emergency_stop():
    """Emergency stop laser"""
    try:
        response = laser_client.emergency_stop()
        update_system_state('emergency_stop', True)
        update_system_state('auto_mode_active', False)
        
        return jsonify({
            'success': response.success,
            'message': response.message,
            'timestamp': time.time()
        })
        
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'Emergency stop error: {str(e)}',
            'timestamp': time.time()
        }), 500

@app.route('/api/laser/auto', methods=['POST'])
def auto_mode():
    """Execute AUTO mode sequence"""
    try:
        if not laser_client.connected:
            return jsonify({
                'success': False,
                'message': 'Laser not connected',
                'timestamp': time.time()
            }), 400
        
        # Start AUTO mode in background thread
        def auto_sequence():
            update_system_state('auto_mode_active', True)
            success = safety_controller.execute_auto_sequence()
            update_system_state('auto_mode_active', False)
            
            socketio.emit('auto_sequence_complete', {
                'success': success,
                'timestamp': time.time()
            })
        
        thread = threading.Thread(target=auto_sequence)
        thread.daemon = True
        thread.start()
        
        return jsonify({
            'success': True,
            'message': 'AUTO mode sequence started',
            'timestamp': time.time()
        })
        
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'AUTO mode error: {str(e)}',
            'timestamp': time.time()
        }), 500

@app.route('/api/door/close', methods=['POST'])
def close_door():
    """Close safety door"""
    try:
        response = laser_client.close_door()
        return jsonify({
            'success': response.success,
            'message': response.message,
            'timestamp': time.time()
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'Door close error: {str(e)}',
            'timestamp': time.time()
        }), 500

@app.route('/api/door/open', methods=['POST'])
def open_door():
    """Open safety door"""
    try:
        response = laser_client.open_door()
        return jsonify({
            'success': response.success,
            'message': response.message,
            'timestamp': time.time()
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'Door open error: {str(e)}',
            'timestamp': time.time()
        }), 500

# SocketIO event handlers
@socketio.on('connect')
def handle_connect():
    """Handle client connection"""
    emit('system_state', system_state)

@socketio.on('request_status')
def handle_status_request():
    """Handle status request"""
    emit('system_state', system_state)

# Static file serving
@app.route('/static/<path:filename>')
def static_files(filename):
    """Serve static files"""
    return send_from_directory('static', filename)

def initialize_system():
    """Initialize system components"""
    try:
        # Create necessary directories
        os.makedirs('static/images', exist_ok=True)
        os.makedirs('templates', exist_ok=True)
        os.makedirs('logs', exist_ok=True)
        
        # Initialize template manager
        template_manager.initialize()
        
        print("System initialized successfully")
        
    except Exception as e:
        print(f"System initialization error: {e}")

if __name__ == '__main__':
    initialize_system()
    
    print(f"Starting Laser Demonstration System...")
    print(f"Web interface: http://{config.web_server.host}:{config.web_server.port}")
    print(f"EZcad3 connection: {config.ezcad3.host}:{config.ezcad3.port}")
    
    socketio.run(app, 
                host=config.web_server.host, 
                port=config.web_server.port, 
                debug=config.web_server.debug)
