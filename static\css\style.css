/* Laser Demonstration System - Custom Styles */

/* Global Styles */
body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background-color: #f8f9fa;
}

/* Header Styles */
.navbar-brand {
    font-weight: bold;
    font-size: 1.5rem;
}

/* Card Styles */
.card {
    border: none;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    transition: transform 0.2s ease-in-out;
}

.card:hover {
    transform: translateY(-2px);
}

.card-header {
    border-bottom: none;
    font-weight: 600;
}

/* Button Styles */
.btn {
    border-radius: 8px;
    font-weight: 500;
    transition: all 0.2s ease-in-out;
}

.btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

.btn-lg {
    padding: 12px 24px;
    font-size: 1.1rem;
}

/* Emergency Stop Button */
#emergency-stop-btn {
    background-color: #dc3545;
    border-color: #dc3545;
    color: white;
    font-weight: bold;
    animation: pulse 2s infinite;
}

#emergency-stop-btn:hover {
    background-color: #c82333;
    border-color: #bd2130;
}

@keyframes pulse {
    0% { box-shadow: 0 0 0 0 rgba(220, 53, 69, 0.7); }
    70% { box-shadow: 0 0 0 10px rgba(220, 53, 69, 0); }
    100% { box-shadow: 0 0 0 0 rgba(220, 53, 69, 0); }
}

/* Status Badge Styles */
.badge {
    font-size: 0.8rem;
    padding: 6px 12px;
}

.badge.bg-success {
    background-color: #28a745 !important;
}

.badge.bg-warning {
    background-color: #ffc107 !important;
    color: #212529 !important;
}

.badge.bg-danger {
    background-color: #dc3545 !important;
}

.badge.bg-secondary {
    background-color: #6c757d !important;
}

/* Progress Bar Styles */
.progress {
    height: 25px;
    border-radius: 12px;
    background-color: #e9ecef;
}

.progress-bar {
    border-radius: 12px;
    font-weight: 600;
    transition: width 0.3s ease-in-out;
}

/* Form Styles */
.form-control, .form-select {
    border-radius: 8px;
    border: 2px solid #e9ecef;
    transition: border-color 0.2s ease-in-out;
}

.form-control:focus, .form-select:focus {
    border-color: #0d6efd;
    box-shadow: 0 0 0 0.2rem rgba(13, 110, 253, 0.25);
}

/* Visitor Information Card */
#visitor-info .card {
    border: 2px solid #28a745;
    background: linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%);
}

/* Camera Modal Styles */
#camera-video {
    border-radius: 12px;
    border: 3px solid #0d6efd;
    max-width: 100%;
    height: auto;
}

#camera-container {
    position: relative;
    display: inline-block;
}

/* Product Showcase Styles */
.card-img-top {
    height: 150px;
    object-fit: cover;
    border-radius: 8px 8px 0 0;
}

/* Status Messages */
.alert {
    border: none;
    border-radius: 12px;
    margin-bottom: 10px;
    animation: slideIn 0.3s ease-in-out;
}

@keyframes slideIn {
    from {
        opacity: 0;
        transform: translateY(-20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Loading Spinner */
.spinner-border-sm {
    width: 1rem;
    height: 1rem;
}

/* Responsive Design */
@media (max-width: 768px) {
    .navbar-brand {
        font-size: 1.2rem;
    }
    
    .btn-lg {
        padding: 10px 20px;
        font-size: 1rem;
    }
    
    .card-body {
        padding: 1rem;
    }
    
    .progress {
        height: 20px;
    }
}

@media (max-width: 576px) {
    .container-fluid {
        padding-left: 10px;
        padding-right: 10px;
    }
    
    .btn-group .btn {
        font-size: 0.9rem;
        padding: 8px 12px;
    }
    
    .card-title {
        font-size: 1.1rem;
    }
    
    h6 {
        font-size: 1rem;
    }
}

/* Touch-friendly styles for tablets */
@media (pointer: coarse) {
    .btn {
        min-height: 44px;
        min-width: 44px;
    }
    
    .form-control, .form-select {
        min-height: 44px;
    }
    
    .btn-group .btn {
        padding: 12px 16px;
    }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
    .card {
        border: 2px solid #000;
    }
    
    .btn {
        border-width: 2px;
    }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
    .btn, .card, .alert {
        transition: none;
    }
    
    @keyframes pulse {
        0%, 100% { box-shadow: 0 0 0 0 rgba(220, 53, 69, 0.7); }
    }
    
    @keyframes slideIn {
        from, to {
            opacity: 1;
            transform: translateY(0);
        }
    }
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
    body {
        background-color: #212529;
        color: #fff;
    }
    
    .card {
        background-color: #343a40;
        color: #fff;
    }
    
    .form-control, .form-select {
        background-color: #495057;
        border-color: #6c757d;
        color: #fff;
    }
    
    .form-control:focus, .form-select:focus {
        background-color: #495057;
        border-color: #0d6efd;
        color: #fff;
    }
}

/* Custom utility classes */
.text-shadow {
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.1);
}

.border-radius-lg {
    border-radius: 12px !important;
}

.shadow-lg-custom {
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15) !important;
}

/* Animation classes */
.fade-in {
    animation: fadeIn 0.5s ease-in-out;
}

@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

.slide-up {
    animation: slideUp 0.3s ease-in-out;
}

@keyframes slideUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Status indicator styles */
.status-indicator {
    display: inline-block;
    width: 12px;
    height: 12px;
    border-radius: 50%;
    margin-right: 8px;
}

.status-indicator.connected {
    background-color: #28a745;
    animation: pulse-green 2s infinite;
}

.status-indicator.disconnected {
    background-color: #dc3545;
}

.status-indicator.warning {
    background-color: #ffc107;
}

@keyframes pulse-green {
    0% { box-shadow: 0 0 0 0 rgba(40, 167, 69, 0.7); }
    70% { box-shadow: 0 0 0 10px rgba(40, 167, 69, 0); }
    100% { box-shadow: 0 0 0 0 rgba(40, 167, 69, 0); }
}
