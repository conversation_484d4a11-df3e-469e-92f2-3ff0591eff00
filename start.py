#!/usr/bin/env python3
"""
Laser Demonstration System - Startup Script
Handles system initialization and startup checks
"""

import os
import sys
import time
import subprocess
import platform
from pathlib import Path

def check_python_version():
    """Check if Python version is compatible"""
    if sys.version_info < (3, 8):
        print("❌ Python 3.8 or higher is required")
        print(f"Current version: {sys.version}")
        return False
    print(f"✅ Python version: {sys.version.split()[0]}")
    return True

def check_dependencies():
    """Check if required dependencies are installed"""
    required_packages = [
        'flask',
        'flask-socketio',
        'opencv-python',
        'pyzbar',
        'qrcode',
        'pillow',
        'requests',
        'python-dotenv',
        'pydantic',
        'websockets',
        'numpy'
    ]
    
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package.replace('-', '_'))
            print(f"✅ {package}")
        except ImportError:
            print(f"❌ {package} - Missing")
            missing_packages.append(package)
    
    if missing_packages:
        print(f"\n❌ Missing packages: {', '.join(missing_packages)}")
        print("Install with: pip install -r requirements.txt")
        return False
    
    return True

def check_camera():
    """Check if camera is available"""
    try:
        import cv2
        cap = cv2.VideoCapture(0)
        if cap.isOpened():
            print("✅ Camera available")
            cap.release()
            return True
        else:
            print("⚠️  Camera not available (optional)")
            return True  # Not critical for operation
    except Exception as e:
        print(f"⚠️  Camera check failed: {e}")
        return True  # Not critical

def create_directories():
    """Create necessary directories"""
    directories = [
        'static/images',
        'templates',
        'logs',
        'temp'
    ]
    
    for directory in directories:
        Path(directory).mkdir(parents=True, exist_ok=True)
        print(f"✅ Directory: {directory}")

def check_config():
    """Check configuration files"""
    if not os.path.exists('.env'):
        print("⚠️  .env file not found, using defaults")
        print("Copy .env.example to .env and customize settings")
    else:
        print("✅ Configuration file found")
    
    return True

def check_ezcad3_connection():
    """Check if EZcad3 is accessible"""
    try:
        import socket
        from config import config
        
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        sock.settimeout(2)
        result = sock.connect_ex((config.ezcad3.host, config.ezcad3.port))
        sock.close()
        
        if result == 0:
            print(f"✅ EZcad3 connection available at {config.ezcad3.host}:{config.ezcad3.port}")
            return True
        else:
            print(f"⚠️  EZcad3 not accessible at {config.ezcad3.host}:{config.ezcad3.port}")
            print("Make sure EZcad3 is running with TCP/IP enabled")
            return True  # Not critical for startup
    except Exception as e:
        print(f"⚠️  EZcad3 connection check failed: {e}")
        return True

def setup_demo_data():
    """Set up demo data if needed"""
    try:
        # Run demo setup if no templates exist
        if not os.path.exists('templates/business_card.json'):
            print("Setting up demo data...")
            subprocess.run([sys.executable, 'demo.py'], check=False)
            print("✅ Demo data created")
        else:
            print("✅ Demo data already exists")
    except Exception as e:
        print(f"⚠️  Demo data setup failed: {e}")

def print_system_info():
    """Print system information"""
    print("\n" + "="*60)
    print("LASER DEMONSTRATION SYSTEM")
    print("="*60)
    print(f"Platform: {platform.system()} {platform.release()}")
    print(f"Python: {sys.version.split()[0]}")
    print(f"Working Directory: {os.getcwd()}")
    print("="*60)

def print_startup_instructions():
    """Print startup instructions"""
    from config import config
    
    print("\n🚀 SYSTEM READY!")
    print("\nStartup Instructions:")
    print("1. Ensure EZcad3 is running with TCP/IP enabled")
    print("2. Access the web interface:")
    print(f"   Local: http://localhost:{config.web_server.port}")
    print(f"   Network: http://{get_local_ip()}:{config.web_server.port}")
    print("\n📱 For tablet/mobile access, use the network URL")
    print("\n🔧 Configuration:")
    print(f"   EZcad3: {config.ezcad3.host}:{config.ezcad3.port}")
    print(f"   Web Server: {config.web_server.host}:{config.web_server.port}")
    print("\n📋 Sample QR codes available in 'sample_qr_codes/' directory")
    print("\n" + "="*60)

def get_local_ip():
    """Get local IP address"""
    try:
        import socket
        s = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
        s.connect(("*******", 80))
        ip = s.getsockname()[0]
        s.close()
        return ip
    except:
        return "localhost"

def main():
    """Main startup function"""
    print_system_info()
    
    print("\n🔍 System Check:")
    print("-" * 30)
    
    # Run all checks
    checks = [
        ("Python Version", check_python_version),
        ("Dependencies", check_dependencies),
        ("Camera", check_camera),
        ("Directories", lambda: (create_directories(), True)[1]),
        ("Configuration", check_config),
        ("EZcad3 Connection", check_ezcad3_connection),
        ("Demo Data", lambda: (setup_demo_data(), True)[1])
    ]
    
    all_passed = True
    for name, check_func in checks:
        print(f"\n{name}:")
        try:
            if not check_func():
                all_passed = False
        except Exception as e:
            print(f"❌ {name} check failed: {e}")
            all_passed = False
    
    print("\n" + "-" * 30)
    
    if all_passed:
        print("✅ All checks passed!")
        print_startup_instructions()
        
        # Ask if user wants to start the server
        try:
            response = input("\nStart the server now? (y/N): ").strip().lower()
            if response in ['y', 'yes']:
                print("\n🚀 Starting server...")
                time.sleep(1)
                os.system(f"{sys.executable} app.py")
            else:
                print("\nTo start manually, run: python app.py")
        except KeyboardInterrupt:
            print("\n\nStartup cancelled.")
    else:
        print("❌ Some checks failed. Please resolve issues before starting.")
        sys.exit(1)

if __name__ == "__main__":
    main()
