#!/usr/bin/env python3
"""
Basic test to verify core functionality works
"""

def test_imports():
    """Test if we can import the main modules"""
    print("Testing imports...")
    
    try:
        from flask import Flask
        print("✅ Flask imported")
    except ImportError as e:
        print(f"❌ Flask import failed: {e}")
        return False
    
    try:
        from flask_socketio import SocketIO
        print("✅ Flask-SocketIO imported")
    except ImportError as e:
        print(f"❌ Flask-SocketIO import failed: {e}")
        return False
    
    try:
        from config import config
        print("✅ Config imported")
        print(f"   EZcad3 host: {config.ezcad3.host}")
        print(f"   EZcad3 port: {config.ezcad3.port}")
    except ImportError as e:
        print(f"❌ Config import failed: {e}")
        return False
    
    try:
        from ezcad3_client import EZcad3Client
        print("✅ EZcad3Client imported")
    except ImportError as e:
        print(f"❌ EZcad3Client import failed: {e}")
        return False
    
    try:
        from scanner import BarcodeScanner
        print("✅ BarcodeScanner imported")
    except ImportError as e:
        print(f"❌ BarcodeScanner import failed: {e}")
        return False
    
    return True

def test_mock_server():
    """Test mock server functionality"""
    print("\nTesting mock server...")
    
    try:
        import importlib.util
        spec = importlib.util.spec_from_file_location("mock_ezcad3_server", "mock_ezcad3_server.py")
        mock_module = importlib.util.module_from_spec(spec)
        spec.loader.exec_module(mock_module)
        
        print("✅ Mock server module loaded")
        
        # Test creating server instance
        server = mock_module.MockEZcad3Server()
        print("✅ Mock server instance created")
        
        # Test command processing
        response = server.process_command("CONNECT")
        print(f"✅ Command processing works: {response}")
        
        return True
        
    except Exception as e:
        print(f"❌ Mock server test failed: {e}")
        return False

def test_scanner():
    """Test scanner functionality"""
    print("\nTesting scanner...")
    
    try:
        from scanner import BarcodeScanner
        
        scanner = BarcodeScanner()
        print("✅ Scanner created")
        
        # Test parsing
        test_data = '{"name": "Test User", "company": "Test Company"}'
        parsed = scanner.parse_scan_data(test_data)
        print(f"✅ JSON parsing works: {parsed['name']}")
        
        return True
        
    except Exception as e:
        print(f"❌ Scanner test failed: {e}")
        return False

def main():
    """Run basic tests"""
    print("=" * 50)
    print("BASIC SYSTEM TEST")
    print("=" * 50)
    
    tests = [
        ("Imports", test_imports),
        ("Mock Server", test_mock_server),
        ("Scanner", test_scanner)
    ]
    
    passed = 0
    for name, test_func in tests:
        print(f"\n--- {name} ---")
        if test_func():
            passed += 1
            print(f"✅ {name}: PASSED")
        else:
            print(f"❌ {name}: FAILED")
    
    print(f"\n" + "=" * 50)
    print(f"RESULTS: {passed}/{len(tests)} tests passed")
    
    if passed == len(tests):
        print("🎉 All basic tests passed!")
        print("\nNext steps:")
        print("1. Try running: python mock_ezcad3_server.py")
        print("2. In another terminal: python app.py")
        print("3. Open browser: http://localhost:5000")
    else:
        print("⚠️  Some tests failed. Check the errors above.")
    
    print("=" * 50)

if __name__ == "__main__":
    main()
