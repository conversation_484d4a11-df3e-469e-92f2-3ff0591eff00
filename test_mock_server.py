#!/usr/bin/env python3
"""
Test script for Mock EZcad3 Server
Tests various commands and responses
"""

import socket
import time
import threading
import json

class MockServerTester:
    """Test client for mock EZcad3 server"""
    
    def __init__(self, host='localhost', port=8888):
        self.host = host
        self.port = port
        self.socket = None
    
    def connect(self):
        """Connect to mock server"""
        try:
            self.socket = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            self.socket.connect((self.host, self.port))
            print(f"✅ Connected to mock server at {self.host}:{self.port}")
            return True
        except Exception as e:
            print(f"❌ Connection failed: {e}")
            return False
    
    def disconnect(self):
        """Disconnect from server"""
        if self.socket:
            self.socket.close()
            self.socket = None
            print("✅ Disconnected from server")
    
    def send_command(self, command):
        """Send command and get response"""
        if not self.socket:
            return "ERROR: Not connected"
        
        try:
            # Send command
            self.socket.send(f"{command}\r\n".encode('utf-8'))
            print(f"📤 Sent: {command}")
            
            # Receive response
            response = self.socket.recv(1024).decode('utf-8').strip()
            print(f"📥 Received: {response}")
            
            return response
        except Exception as e:
            print(f"❌ Command error: {e}")
            return f"ERROR: {e}"
    
    def test_basic_commands(self):
        """Test basic command functionality"""
        print("\n" + "="*50)
        print("TESTING BASIC COMMANDS")
        print("="*50)
        
        commands = [
            "CONNECT",
            "GETSTATUS",
            "LOADFILE test_template.ezd",
            "SETVAR name John Doe",
            "SETVAR company Test Company",
            "GETSTATUS",
            "DISCONNECT"
        ]
        
        for cmd in commands:
            response = self.send_command(cmd)
            time.sleep(0.5)  # Small delay between commands
    
    def test_marking_sequence(self):
        """Test complete marking sequence"""
        print("\n" + "="*50)
        print("TESTING MARKING SEQUENCE")
        print("="*50)
        
        # Connect and setup
        self.send_command("CONNECT")
        self.send_command("LOADFILE business_card.ezd")
        self.send_command("SETVAR name Jane Smith")
        self.send_command("SETVAR company ABC Corp")
        
        # Close door
        print("\n🚪 Closing door...")
        self.send_command("CLOSEDOOR")
        time.sleep(3)  # Wait for door to close
        
        # Start marking
        print("\n🔥 Starting marking...")
        self.send_command("STARTMARK")
        
        # Monitor progress
        print("\n📊 Monitoring progress...")
        for i in range(12):  # Monitor for 12 seconds
            response = self.send_command("GETPROGRESS")
            time.sleep(1)
            
            # Check if completed
            if "100" in response:
                print("✅ Marking completed!")
                break
        
        # Open door
        print("\n🚪 Opening door...")
        self.send_command("OPENDOOR")
        time.sleep(3)  # Wait for door to open
        
        print("✅ Marking sequence completed!")
    
    def test_safety_features(self):
        """Test safety features"""
        print("\n" + "="*50)
        print("TESTING SAFETY FEATURES")
        print("="*50)
        
        # Test emergency stop
        print("\n🚨 Testing emergency stop...")
        self.send_command("CONNECT")
        self.send_command("LOADFILE test.ezd")
        self.send_command("CLOSEDOOR")
        time.sleep(2)
        self.send_command("STARTMARK")
        time.sleep(2)
        self.send_command("ESTOP")
        self.send_command("GETSTATUS")
        
        # Test door safety
        print("\n🚪 Testing door safety...")
        self.send_command("OPENDOOR")
        time.sleep(2)
        response = self.send_command("STARTMARK")
        if "ERROR" in response and "door" in response.lower():
            print("✅ Door safety working correctly")
        else:
            print("❌ Door safety not working")
    
    def test_parameter_setting(self):
        """Test parameter setting commands"""
        print("\n" + "="*50)
        print("TESTING PARAMETER SETTING")
        print("="*50)
        
        self.send_command("CONNECT")
        self.send_command("SETPOWER 75")
        self.send_command("SETSPEED 150")
        self.send_command("SETFREQ 25000")
        self.send_command("GETSTATUS")
    
    def run_all_tests(self):
        """Run all tests"""
        print("🧪 MOCK EZCAD3 SERVER TESTING")
        print("="*60)
        
        if not self.connect():
            return False
        
        try:
            self.test_basic_commands()
            self.test_parameter_setting()
            self.test_safety_features()
            self.test_marking_sequence()
            
            print("\n" + "="*60)
            print("✅ ALL TESTS COMPLETED SUCCESSFULLY!")
            print("Mock server is working correctly.")
            print("="*60)
            
        except Exception as e:
            print(f"\n❌ Test failed: {e}")
        finally:
            self.disconnect()

def test_with_laser_demo_system():
    """Test integration with the actual laser demo system"""
    print("\n" + "="*60)
    print("TESTING INTEGRATION WITH LASER DEMO SYSTEM")
    print("="*60)
    
    try:
        from ezcad3_client import EZcad3Client
        
        # Create client
        client = EZcad3Client(host='localhost', port=8888)
        
        print("🔌 Testing connection...")
        response = client.connect()
        print(f"Connection: {response.success} - {response.message}")
        
        if response.success:
            print("\n📁 Testing file loading...")
            response = client.load_template("test_template.ezd")
            print(f"Load template: {response.success} - {response.message}")
            
            print("\n📝 Testing variable setting...")
            response = client.set_text_variable("name", "Integration Test")
            print(f"Set variable: {response.success} - {response.message}")
            
            print("\n📊 Testing status...")
            response = client.get_status()
            print(f"Get status: {response.success} - {response.message}")
            
            print("\n🔥 Testing marking...")
            response = client.close_door()
            print(f"Close door: {response.success} - {response.message}")
            
            time.sleep(2)
            
            response = client.start_marking()
            print(f"Start marking: {response.success} - {response.message}")
            
            # Monitor progress
            for i in range(5):
                response = client.get_progress()
                print(f"Progress: {response.success} - {response.message}")
                time.sleep(1)
            
            response = client.stop_marking()
            print(f"Stop marking: {response.success} - {response.message}")
            
            response = client.open_door()
            print(f"Open door: {response.success} - {response.message}")
            
            print("\n🔌 Testing disconnection...")
            response = client.disconnect()
            print(f"Disconnect: {response.success} - {response.message}")
            
            print("\n✅ INTEGRATION TEST SUCCESSFUL!")
        
    except ImportError:
        print("❌ Cannot import ezcad3_client. Make sure the laser demo system is available.")
    except Exception as e:
        print(f"❌ Integration test failed: {e}")

def main():
    """Main test function"""
    import argparse
    
    parser = argparse.ArgumentParser(description='Test Mock EZcad3 Server')
    parser.add_argument('--host', default='localhost', help='Server host')
    parser.add_argument('--port', type=int, default=8888, help='Server port')
    parser.add_argument('--integration', action='store_true', help='Run integration test with laser demo system')
    
    args = parser.parse_args()
    
    if args.integration:
        test_with_laser_demo_system()
    else:
        tester = MockServerTester(args.host, args.port)
        tester.run_all_tests()

if __name__ == "__main__":
    main()
