#!/usr/bin/env python3
"""
Mock EZcad3 TCP Server
Simulates EZcad3 laser software for testing and demonstration purposes
"""

import socket
import threading
import time
import json
import logging
from typing import Dict, Any
from enum import Enum
from dataclasses import dataclass

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class MockLaserState(Enum):
    """Mock laser system states"""
    DISCONNECTED = "disconnected"
    IDLE = "idle"
    MARKING = "marking"
    PAUSED = "paused"
    ERROR = "error"
    DOOR_OPEN = "door_open"
    DOOR_CLOSED = "door_closed"

@dataclass
class MockLaserStatus:
    """Mock laser status information"""
    state: MockLaserState = MockLaserState.IDLE
    progress: int = 0
    current_file: str = ""
    door_closed: bool = True
    emergency_stop: bool = False
    power: int = 50
    speed: int = 100
    frequency: int = 20000
    variables: Dict[str, str] = None
    
    def __post_init__(self):
        if self.variables is None:
            self.variables = {}

class MockEZcad3Server:
    """Mock EZcad3 TCP server for testing"""
    
    def __init__(self, host='localhost', port=8888):
        self.host = host
        self.port = port
        self.server_socket = None
        self.running = False
        self.clients = []
        
        # Mock laser status
        self.status = MockLaserStatus()
        
        # Simulation parameters
        self.marking_duration = 10  # seconds
        self.door_operation_time = 2  # seconds
        
        logger.info(f"Mock EZcad3 Server initialized on {host}:{port}")
    
    def start(self):
        """Start the mock server"""
        try:
            self.server_socket = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            self.server_socket.setsockopt(socket.SOL_SOCKET, socket.SO_REUSEADDR, 1)
            self.server_socket.bind((self.host, self.port))
            self.server_socket.listen(5)
            
            self.running = True
            logger.info(f"Mock EZcad3 Server started on {self.host}:{self.port}")
            
            while self.running:
                try:
                    client_socket, address = self.server_socket.accept()
                    logger.info(f"Client connected from {address}")
                    
                    client_thread = threading.Thread(
                        target=self.handle_client,
                        args=(client_socket, address)
                    )
                    client_thread.daemon = True
                    client_thread.start()
                    
                except socket.error as e:
                    if self.running:
                        logger.error(f"Server socket error: {e}")
                    break
                    
        except Exception as e:
            logger.error(f"Server start error: {e}")
        finally:
            self.stop()
    
    def stop(self):
        """Stop the mock server"""
        self.running = False
        if self.server_socket:
            self.server_socket.close()
        logger.info("Mock EZcad3 Server stopped")
    
    def handle_client(self, client_socket, address):
        """Handle client connection"""
        try:
            while self.running:
                data = client_socket.recv(1024).decode('utf-8').strip()
                if not data:
                    break
                
                logger.info(f"Received from {address}: {data}")
                
                # Process command and generate response
                response = self.process_command(data)
                
                # Send response
                client_socket.send(f"{response}\r\n".encode('utf-8'))
                logger.info(f"Sent to {address}: {response}")
                
        except Exception as e:
            logger.error(f"Client handler error: {e}")
        finally:
            client_socket.close()
            logger.info(f"Client {address} disconnected")
    
    def process_command(self, command: str) -> str:
        """Process incoming command and return response"""
        parts = command.split()
        if not parts:
            return "ERROR: Empty command"
        
        cmd = parts[0].upper()
        params = parts[1:] if len(parts) > 1 else []
        
        try:
            if cmd == "CONNECT":
                return self.handle_connect()
            elif cmd == "DISCONNECT":
                return self.handle_disconnect()
            elif cmd == "LOADFILE":
                return self.handle_load_file(params)
            elif cmd == "STARTMARK":
                return self.handle_start_mark()
            elif cmd == "STOPMARK":
                return self.handle_stop_mark()
            elif cmd == "PAUSEMARK":
                return self.handle_pause_mark()
            elif cmd == "RESUMEMARK":
                return self.handle_resume_mark()
            elif cmd == "GETSTATUS":
                return self.handle_get_status()
            elif cmd == "GETPROGRESS":
                return self.handle_get_progress()
            elif cmd == "SETVAR":
                return self.handle_set_variable(params)
            elif cmd == "SETTEXT":
                return self.handle_set_text(params)
            elif cmd == "CLOSEDOOR":
                return self.handle_close_door()
            elif cmd == "OPENDOOR":
                return self.handle_open_door()
            elif cmd == "ESTOP":
                return self.handle_emergency_stop()
            elif cmd == "SETPOWER":
                return self.handle_set_power(params)
            elif cmd == "SETSPEED":
                return self.handle_set_speed(params)
            elif cmd == "SETFREQ":
                return self.handle_set_frequency(params)
            else:
                return f"ERROR: Unknown command '{cmd}'"
                
        except Exception as e:
            logger.error(f"Command processing error: {e}")
            return f"ERROR: Command processing failed - {str(e)}"
    
    def handle_connect(self) -> str:
        """Handle connection command"""
        self.status.state = MockLaserState.IDLE
        return "OK: Connected to Mock EZcad3"
    
    def handle_disconnect(self) -> str:
        """Handle disconnection command"""
        self.status.state = MockLaserState.DISCONNECTED
        return "OK: Disconnected"
    
    def handle_load_file(self, params) -> str:
        """Handle file loading command"""
        if not params:
            return "ERROR: No filename specified"
        
        filename = params[0]
        self.status.current_file = filename
        logger.info(f"Loaded file: {filename}")
        return f"OK: File '{filename}' loaded"
    
    def handle_start_mark(self) -> str:
        """Handle start marking command"""
        if self.status.state == MockLaserState.MARKING:
            return "ERROR: Already marking"
        
        if not self.status.current_file:
            return "ERROR: No file loaded"
        
        if not self.status.door_closed:
            return "ERROR: Safety door is open"
        
        if self.status.emergency_stop:
            return "ERROR: Emergency stop is active"
        
        # Start marking simulation
        self.status.state = MockLaserState.MARKING
        self.status.progress = 0
        
        # Start progress simulation in background
        threading.Thread(target=self.simulate_marking, daemon=True).start()
        
        return "OK: Marking started"
    
    def handle_stop_mark(self) -> str:
        """Handle stop marking command"""
        if self.status.state != MockLaserState.MARKING:
            return "ERROR: Not currently marking"
        
        self.status.state = MockLaserState.IDLE
        self.status.progress = 0
        return "OK: Marking stopped"
    
    def handle_pause_mark(self) -> str:
        """Handle pause marking command"""
        if self.status.state != MockLaserState.MARKING:
            return "ERROR: Not currently marking"
        
        self.status.state = MockLaserState.PAUSED
        return "OK: Marking paused"
    
    def handle_resume_mark(self) -> str:
        """Handle resume marking command"""
        if self.status.state != MockLaserState.PAUSED:
            return "ERROR: Not currently paused"
        
        self.status.state = MockLaserState.MARKING
        return "OK: Marking resumed"
    
    def handle_get_status(self) -> str:
        """Handle status query command"""
        status_data = {
            "state": self.status.state.value,
            "progress": self.status.progress,
            "file": self.status.current_file,
            "door_closed": self.status.door_closed,
            "emergency_stop": self.status.emergency_stop,
            "power": self.status.power,
            "speed": self.status.speed,
            "frequency": self.status.frequency
        }
        return f"OK: STATUS {json.dumps(status_data)}"
    
    def handle_get_progress(self) -> str:
        """Handle progress query command"""
        return f"OK: PROGRESS {self.status.progress}"
    
    def handle_set_variable(self, params) -> str:
        """Handle set variable command"""
        if len(params) < 2:
            return "ERROR: Variable name and value required"
        
        var_name = params[0]
        var_value = " ".join(params[1:])  # Join remaining parts as value
        
        self.status.variables[var_name] = var_value
        logger.info(f"Set variable: {var_name} = {var_value}")
        return f"OK: Variable '{var_name}' set to '{var_value}'"
    
    def handle_set_text(self, params) -> str:
        """Handle set text command (alias for set variable)"""
        return self.handle_set_variable(params)
    
    def handle_close_door(self) -> str:
        """Handle close door command"""
        if self.status.door_closed:
            return "OK: Door already closed"
        
        # Simulate door closing
        threading.Thread(target=self.simulate_door_close, daemon=True).start()
        return "OK: Closing door"
    
    def handle_open_door(self) -> str:
        """Handle open door command"""
        if not self.status.door_closed:
            return "OK: Door already open"
        
        # Simulate door opening
        threading.Thread(target=self.simulate_door_open, daemon=True).start()
        return "OK: Opening door"
    
    def handle_emergency_stop(self) -> str:
        """Handle emergency stop command"""
        self.status.emergency_stop = True
        self.status.state = MockLaserState.ERROR
        self.status.progress = 0
        logger.warning("Emergency stop activated!")
        return "OK: Emergency stop activated"
    
    def handle_set_power(self, params) -> str:
        """Handle set power command"""
        if not params:
            return "ERROR: Power value required"
        
        try:
            power = int(params[0])
            if 0 <= power <= 100:
                self.status.power = power
                return f"OK: Power set to {power}%"
            else:
                return "ERROR: Power must be 0-100%"
        except ValueError:
            return "ERROR: Invalid power value"
    
    def handle_set_speed(self, params) -> str:
        """Handle set speed command"""
        if not params:
            return "ERROR: Speed value required"
        
        try:
            speed = int(params[0])
            if speed > 0:
                self.status.speed = speed
                return f"OK: Speed set to {speed}"
            else:
                return "ERROR: Speed must be positive"
        except ValueError:
            return "ERROR: Invalid speed value"
    
    def handle_set_frequency(self, params) -> str:
        """Handle set frequency command"""
        if not params:
            return "ERROR: Frequency value required"
        
        try:
            frequency = int(params[0])
            if frequency > 0:
                self.status.frequency = frequency
                return f"OK: Frequency set to {frequency} Hz"
            else:
                return "ERROR: Frequency must be positive"
        except ValueError:
            return "ERROR: Invalid frequency value"
    
    def simulate_marking(self):
        """Simulate marking progress"""
        start_time = time.time()
        
        while (self.status.state == MockLaserState.MARKING and 
               time.time() - start_time < self.marking_duration):
            
            elapsed = time.time() - start_time
            progress = int((elapsed / self.marking_duration) * 100)
            self.status.progress = min(progress, 100)
            
            time.sleep(0.1)  # Update every 100ms
        
        if self.status.state == MockLaserState.MARKING:
            self.status.progress = 100
            self.status.state = MockLaserState.IDLE
            logger.info("Marking simulation completed")
    
    def simulate_door_close(self):
        """Simulate door closing"""
        self.status.state = MockLaserState.DOOR_CLOSED
        time.sleep(self.door_operation_time)
        self.status.door_closed = True
        if self.status.state == MockLaserState.DOOR_CLOSED:
            self.status.state = MockLaserState.IDLE
        logger.info("Door closed")
    
    def simulate_door_open(self):
        """Simulate door opening"""
        self.status.state = MockLaserState.DOOR_OPEN
        time.sleep(self.door_operation_time)
        self.status.door_closed = False
        if self.status.state == MockLaserState.DOOR_OPEN:
            self.status.state = MockLaserState.IDLE
        logger.info("Door opened")

def main():
    """Main function to run the mock server"""
    import argparse
    
    parser = argparse.ArgumentParser(description='Mock EZcad3 TCP Server')
    parser.add_argument('--host', default='localhost', help='Server host (default: localhost)')
    parser.add_argument('--port', type=int, default=8888, help='Server port (default: 8888)')
    parser.add_argument('--marking-time', type=int, default=10, help='Marking simulation time in seconds (default: 10)')
    parser.add_argument('--door-time', type=int, default=2, help='Door operation time in seconds (default: 2)')
    
    args = parser.parse_args()
    
    # Create and configure server
    server = MockEZcad3Server(args.host, args.port)
    server.marking_duration = args.marking_time
    server.door_operation_time = args.door_time
    
    print("=" * 60)
    print("MOCK EZCAD3 TCP SERVER")
    print("=" * 60)
    print(f"Host: {args.host}")
    print(f"Port: {args.port}")
    print(f"Marking simulation time: {args.marking_time} seconds")
    print(f"Door operation time: {args.door_time} seconds")
    print("=" * 60)
    print("Server is starting...")
    print("Press Ctrl+C to stop")
    print("=" * 60)
    
    try:
        server.start()
    except KeyboardInterrupt:
        print("\nShutting down server...")
        server.stop()
        print("Server stopped.")

if __name__ == "__main__":
    main()
