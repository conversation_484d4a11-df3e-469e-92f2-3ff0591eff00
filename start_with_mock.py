#!/usr/bin/env python3
"""
Laser Demonstration System - Start with Mock EZcad3 Server
Automatically starts mock server and main application
"""

import os
import sys
import time
import subprocess
import threading
import signal
from pathlib import Path

def start_mock_server(host='localhost', port=8888):
    """Start mock EZcad3 server in background"""
    print(f"🔧 Starting Mock EZcad3 Server on {host}:{port}...")
    
    try:
        # Start mock server process
        process = subprocess.Popen([
            sys.executable, 'mock_ezcad3_server.py',
            '--host', host,
            '--port', str(port)
        ], stdout=subprocess.PIPE, stderr=subprocess.PIPE)
        
        # Give server time to start
        time.sleep(2)
        
        # Check if process is still running
        if process.poll() is None:
            print(f"✅ Mock EZcad3 Server started successfully (PID: {process.pid})")
            return process
        else:
            stdout, stderr = process.communicate()
            print(f"❌ Mock server failed to start:")
            print(f"STDOUT: {stdout.decode()}")
            print(f"STDERR: {stderr.decode()}")
            return None
            
    except Exception as e:
        print(f"❌ Failed to start mock server: {e}")
        return None

def test_mock_server(host='localhost', port=8888, timeout=5):
    """Test if mock server is responding"""
    import socket
    
    print(f"🧪 Testing mock server connection...")
    
    start_time = time.time()
    while time.time() - start_time < timeout:
        try:
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.settimeout(1)
            result = sock.connect_ex((host, port))
            sock.close()
            
            if result == 0:
                print("✅ Mock server is responding")
                return True
                
        except Exception:
            pass
        
        time.sleep(0.5)
    
    print("❌ Mock server is not responding")
    return False

def setup_mock_environment():
    """Set up environment for mock mode"""
    print("🔧 Setting up mock environment...")
    
    # Create or update .env file
    env_file = Path('.env')
    env_content = []
    
    if env_file.exists():
        with open(env_file, 'r') as f:
            env_content = f.readlines()
    
    # Update or add MOCK_MODE setting
    mock_mode_set = False
    for i, line in enumerate(env_content):
        if line.startswith('MOCK_MODE='):
            env_content[i] = 'MOCK_MODE=True\n'
            mock_mode_set = True
            break
    
    if not mock_mode_set:
        env_content.append('MOCK_MODE=True\n')
    
    # Write updated content
    with open(env_file, 'w') as f:
        f.writelines(env_content)
    
    print("✅ Environment configured for mock mode")

def start_main_application():
    """Start the main laser demo application"""
    print("🚀 Starting Laser Demonstration System...")
    
    try:
        # Start main application
        process = subprocess.Popen([sys.executable, 'app.py'])
        return process
        
    except Exception as e:
        print(f"❌ Failed to start main application: {e}")
        return None

def cleanup_processes(processes):
    """Clean up background processes"""
    print("\n🧹 Cleaning up processes...")
    
    for process in processes:
        if process and process.poll() is None:
            try:
                process.terminate()
                process.wait(timeout=5)
                print(f"✅ Process {process.pid} terminated")
            except subprocess.TimeoutExpired:
                process.kill()
                print(f"⚠️  Process {process.pid} killed (forced)")
            except Exception as e:
                print(f"❌ Error terminating process {process.pid}: {e}")

def print_startup_info():
    """Print startup information"""
    print("\n" + "="*60)
    print("LASER DEMONSTRATION SYSTEM - MOCK MODE")
    print("="*60)
    print("🔧 Mock EZcad3 Server: Running")
    print("🌐 Web Interface: http://localhost:5000")
    print("📱 Mobile/Tablet: http://[your-ip]:5000")
    print("="*60)
    print("✨ SYSTEM READY FOR DEMONSTRATION!")
    print("="*60)
    print("\n📋 Demo Features Available:")
    print("  • Visitor badge scanning (camera or manual)")
    print("  • Template selection and loading")
    print("  • AUTO mode sequence simulation")
    print("  • Safety door controls")
    print("  • Progress monitoring")
    print("  • Emergency stop testing")
    print("\n🧪 This is running in MOCK MODE")
    print("   No real laser hardware is required")
    print("\n⌨️  Press Ctrl+C to stop all services")
    print("="*60)

def main():
    """Main function"""
    import argparse
    
    parser = argparse.ArgumentParser(description='Start Laser Demo System with Mock EZcad3')
    parser.add_argument('--host', default='localhost', help='Mock server host')
    parser.add_argument('--port', type=int, default=8888, help='Mock server port')
    parser.add_argument('--no-mock', action='store_true', help='Skip mock server startup')
    parser.add_argument('--test-only', action='store_true', help='Only test mock server')
    
    args = parser.parse_args()
    
    processes = []
    
    try:
        if not args.no_mock:
            # Set up mock environment
            setup_mock_environment()
            
            # Start mock server
            mock_process = start_mock_server(args.host, args.port)
            if mock_process:
                processes.append(mock_process)
                
                # Test mock server
                if not test_mock_server(args.host, args.port):
                    print("❌ Mock server test failed")
                    return 1
            else:
                print("❌ Failed to start mock server")
                return 1
        
        if args.test_only:
            print("✅ Mock server test completed successfully")
            return 0
        
        # Start main application
        main_process = start_main_application()
        if main_process:
            processes.append(main_process)
            
            # Print startup info
            print_startup_info()
            
            # Wait for main process
            main_process.wait()
        else:
            print("❌ Failed to start main application")
            return 1
            
    except KeyboardInterrupt:
        print("\n🛑 Shutdown requested by user")
    except Exception as e:
        print(f"\n❌ Unexpected error: {e}")
    finally:
        cleanup_processes(processes)
    
    print("👋 Goodbye!")
    return 0

if __name__ == "__main__":
    sys.exit(main())
