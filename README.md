# Laser Demonstration System

A comprehensive exhibition system for laser engraving demonstrations with tablet/mobile interface and EZcad3 integration.

## Features

- **TCP Integration**: Direct communication with EZcad3 laser software
- **Web Interface**: Tablet-friendly interface for visitor interaction
- **Barcode/QR Scanning**: Camera and hardware scanner support
- **Template Management**: Dynamic text replacement in EZcad3 templates
- **Safety Controls**: AUTO mode with door interlocks and progress monitoring
- **Exhibition Ready**: Professional interface for trade shows

## System Architecture

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Tablet/Mobile │◄──►│   Web Server    │◄──►│     EZcad3      │
│   Interface     │    │   (Flask)       │    │   TCP Server    │
└─────────────────┘    └─────────────────┘    └─────────────────┘
                              │                        │
                              ▼                        ▼
                       ┌─────────────────┐    ┌─────────────────┐
                       │ Barcode Scanner │    │  Laser System   │
                       │   (Camera/HW)   │    │   Hardware      │
                       └─────────────────┘    └─────────────────┘
```

## Quick Start

### Option 1: Demo Mode (No EZcad3 Required)
```bash
# Start with mock EZcad3 server for testing/demo
python start_with_mock.py
```
This automatically starts a mock EZcad3 server and the web interface.

### Option 2: Production Mode (Real EZcad3)
```bash
# Run the setup script
python start.py
```
This will check dependencies, create directories, and set up demo data.

### Option 3: Manual Installation
```bash
# Install dependencies
pip install -r requirements.txt

# Configure environment
cp .env.example .env
# Edit .env with your specific settings

# Set up demo data
python demo.py

# Start the application
python app.py
```

## Detailed Installation

### Prerequisites

- **Python 3.8+** (required)
- **EZcad3 software** with TCP/IP control enabled
- **Camera** (optional, for QR code scanning)
- **Network connection** for tablet/mobile access

### Step-by-Step Installation

1. **Download and extract** the system files to your desired directory

2. **Install Python dependencies:**
   ```bash
   pip install -r requirements.txt
   ```

3. **Configure the system:**
   ```bash
   cp .env.example .env
   ```
   Edit `.env` file with your settings:
   ```
   EZCAD3_HOST=*************  # IP of EZcad3 machine
   EZCAD3_PORT=8888           # EZcad3 TCP port
   WEB_PORT=5000              # Web interface port
   ```

4. **Set up EZcad3:**
   - Open EZcad3 software
   - Enable TCP/IP control in settings
   - Note the IP address and port number
   - Update `.env` file with these settings

5. **Create demo data:**
   ```bash
   python demo.py
   ```
   This creates sample templates and QR codes for testing.

6. **Start the system:**
   ```bash
   python app.py
   ```

## Mock EZcad3 Server (For Testing)

The system includes a mock EZcad3 server for testing and demonstration without real hardware.

### Features of Mock Server:
- **Full TCP Protocol Simulation** - Responds to all EZcad3 commands
- **Realistic Timing** - Simulates actual marking and door operation times
- **Progress Simulation** - Shows realistic marking progress
- **Safety Features** - Implements door interlocks and emergency stop
- **Parameter Setting** - Supports power, speed, and frequency settings
- **Variable Management** - Handles template variables

### Using Mock Mode:

1. **Automatic Startup:**
   ```bash
   python start_with_mock.py
   ```

2. **Manual Mock Server:**
   ```bash
   # Terminal 1: Start mock server
   python mock_ezcad3_server.py

   # Terminal 2: Start main application with mock mode
   MOCK_MODE=True python app.py
   ```

3. **Test Mock Server:**
   ```bash
   # Test mock server functionality
   python test_mock_server.py

   # Test integration with laser demo system
   python test_mock_server.py --integration
   ```

### Mock Server Commands:
- `CONNECT` - Connect to mock laser
- `LOADFILE <filename>` - Load template file
- `SETVAR <name> <value>` - Set template variable
- `STARTMARK` - Start marking simulation
- `STOPMARK` - Stop marking
- `GETSTATUS` - Get system status
- `GETPROGRESS` - Get marking progress
- `CLOSEDOOR` / `OPENDOOR` - Door controls
- `ESTOP` - Emergency stop
- `SETPOWER <0-100>` - Set laser power
- `SETSPEED <value>` - Set marking speed
- `SETFREQ <hz>` - Set pulse frequency

## Configuration

Edit the `.env` file to match your setup:

- `EZCAD3_HOST`: IP address of machine running EZcad3
- `EZCAD3_PORT`: TCP port for EZcad3 communication
- `WEB_PORT`: Port for web interface (default: 5000)
- `TEMPLATES_DIR`: Directory containing EZcad3 template files

## Usage

1. **Start EZcad3** and enable TCP/IP control
2. **Run the demonstration system:**
   ```bash
   python app.py
   ```
3. **Access the interface** at `http://localhost:5000` or your configured IP

## Template Variables

Templates support dynamic text replacement using `{{variable}}` syntax:

- `{{name}}`: Visitor name
- `{{company}}`: Company name
- `{{email}}`: Email address
- `{{date}}`: Current date
- `{{time}}`: Current time

## API Endpoints

- `GET /`: Main interface
- `POST /api/scan`: Process barcode/QR scan
- `POST /api/laser/start`: Start marking process
- `POST /api/laser/stop`: Stop marking process
- `POST /api/laser/auto`: Execute AUTO mode sequence
- `GET /api/status`: Get system status

## Safety Features

- Emergency stop functionality
- Safety door interlocks
- Timeout protection
- Real-time status monitoring
- Error handling and recovery

## Exhibition Mode

The system includes special features for exhibition use:

- Minimal operator intervention required
- Clear visual feedback for visitors
- Professional appearance
- Quick reset between demonstrations
- Progress indication during marking

## Exhibition Setup Guide

### For Trade Shows and Demonstrations

1. **Hardware Setup:**
   - Position laser system for visitor visibility
   - Connect laptop/PC to laser system
   - Set up tablet/iPad for visitor interaction
   - Ensure stable network connection

2. **Software Configuration:**
   - Set `EXHIBITION_MODE=True` in `.env`
   - Configure auto-reset timers
   - Load appropriate templates
   - Test all functionality before event

3. **Visitor Flow:**
   - Visitor scans badge/QR code on tablet
   - System processes visitor information
   - Visitor selects template (or auto-selected)
   - Operator initiates AUTO sequence
   - System demonstrates laser marking
   - Visitor receives marked item

### Network Setup for Tablets

1. **WiFi Hotspot Method:**
   ```bash
   # Create WiFi hotspot on laptop
   # Connect tablet to hotspot
   # Access: http://192.168.x.x:5000
   ```

2. **Ethernet Connection:**
   ```bash
   # Connect laptop to venue network
   # Note laptop IP address
   # Access from tablet: http://[laptop-ip]:5000
   ```

## Advanced Configuration

### Custom Templates

1. **Create EZcad3 template (.ezd file)**
2. **Create metadata file (.json):**
   ```json
   {
     "name": "Custom Template",
     "description": "Template description",
     "category": "Custom",
     "variables": [
       {
         "name": "variable_name",
         "display_name": "Display Name",
         "type": "text",
         "required": true,
         "max_length": 50
       }
     ]
   }
   ```
3. **Place both files in `templates/` directory**
4. **Restart application to load new template**

### Safety Configuration

Edit `config.py` to adjust safety settings:
```python
@dataclass
class LaserConfig:
    safety_door_timeout: int = 10    # Door operation timeout
    marking_timeout: int = 300       # Maximum marking time
    auto_mode_enabled: bool = True   # Enable AUTO mode
    emergency_stop_enabled: bool = True
```

### Branding Customization

1. **Replace logo:** `static/images/logo.png`
2. **Update company info:** Edit `.env` file
3. **Customize colors:** Modify `static/css/style.css`
4. **Add product images:** `static/images/laser1.jpg`, `laser2.jpg`

## API Reference

### REST Endpoints

- `GET /api/status` - Get system status
- `POST /api/connect` - Connect to EZcad3
- `POST /api/disconnect` - Disconnect from EZcad3
- `POST /api/scan` - Process barcode/QR scan
- `GET /api/templates` - Get available templates
- `POST /api/template/load` - Load template with variables
- `POST /api/laser/start` - Start marking
- `POST /api/laser/stop` - Stop marking
- `POST /api/laser/auto` - Execute AUTO sequence
- `POST /api/laser/emergency_stop` - Emergency stop

### WebSocket Events

- `system_state_update` - System state changes
- `progress_update` - Marking progress updates
- `auto_sequence_complete` - AUTO sequence completion
- `auto_state_update` - AUTO sequence state changes

## Troubleshooting

### Common Issues

1. **Cannot connect to EZcad3:**
   - Verify EZcad3 TCP/IP is enabled
   - Check IP address and port settings
   - Ensure firewall allows connections
   - Test with telnet: `telnet [ip] [port]`

2. **Camera not working:**
   - Check camera permissions in browser
   - Verify camera index in configuration
   - Test with different camera devices
   - Try different browsers (Chrome recommended)

3. **Templates not loading:**
   - Verify templates directory exists
   - Check file permissions
   - Ensure JSON metadata is valid
   - Check application logs

4. **Tablet cannot access interface:**
   - Verify network connectivity
   - Check firewall settings
   - Ensure correct IP address
   - Try different port if blocked

5. **AUTO sequence fails:**
   - Check EZcad3 connection
   - Verify template is loaded
   - Check safety interlocks
   - Review error messages

### Debug Mode

Enable debug mode for troubleshooting:
```bash
# Set in .env file
DEBUG=True

# Or run with debug flag
python app.py --debug
```

### Log Files

Check log files in `logs/` directory:
- `app.log` - Application logs
- `ezcad3.log` - EZcad3 communication logs
- `scanner.log` - Scanner operation logs

## Support and Maintenance

### Regular Maintenance

1. **Clean camera lens** regularly
2. **Update templates** as needed
3. **Check network connectivity**
4. **Backup configuration files**
5. **Monitor system performance**

### Updates

To update the system:
1. Backup current configuration
2. Download new version
3. Copy configuration files
4. Run setup script
5. Test functionality

## License

MIT License - See LICENSE file for details

## Contributing

1. Fork the repository
2. Create feature branch
3. Make changes
4. Test thoroughly
5. Submit pull request

## Contact

For support and questions:
- Email: <EMAIL>
- Documentation: https://docs.yourcompany.com
- Issues: https://github.com/yourcompany/laser-demo/issues
