#!/usr/bin/env python3
"""
Laser Demonstration System - Demo Script
Creates sample QR codes and tests system functionality
"""

import qrcode
import json
import os
from datetime import datetime
from scanner import BarcodeScanner

def create_sample_qr_codes():
    """Create sample QR codes for testing"""
    
    # Sample visitor data
    visitors = [
        {
            "name": "<PERSON>",
            "company": "Tech Solutions Inc",
            "email": "<EMAIL>",
            "title": "Senior Engineer",
            "phone": "+****************",
            "type": "visitor_badge"
        },
        {
            "name": "<PERSON>",
            "company": "Innovation Labs",
            "email": "<EMAIL>",
            "title": "Product Manager",
            "phone": "+****************",
            "type": "visitor_badge"
        },
        {
            "name": "<PERSON>",
            "company": "Global Manufacturing",
            "email": "<EMAIL>",
            "title": "Operations Director",
            "phone": "+****************",
            "type": "visitor_badge"
        },
        {
            "name": "<PERSON>",
            "company": "Design Studio",
            "email": "<EMAIL>",
            "title": "Creative Director",
            "phone": "+****************",
            "type": "visitor_badge"
        }
    ]
    
    # Create QR codes directory
    os.makedirs('sample_qr_codes', exist_ok=True)
    
    for i, visitor in enumerate(visitors):
        # Add timestamp
        visitor['timestamp'] = datetime.now().isoformat()
        
        # Create QR code
        qr_data = json.dumps(visitor)
        qr = qrcode.QRCode(
            version=1,
            error_correction=qrcode.constants.ERROR_CORRECT_L,
            box_size=10,
            border=4,
        )
        qr.add_data(qr_data)
        qr.make(fit=True)
        
        # Create image
        img = qr.make_image(fill_color="black", back_color="white")
        filename = f"sample_qr_codes/visitor_{i+1}_{visitor['name'].replace(' ', '_').lower()}.png"
        img.save(filename)
        
        print(f"Created QR code: {filename}")
        print(f"Data: {visitor['name']} - {visitor['company']}")
        print()

def test_scanner():
    """Test the barcode scanner functionality"""
    print("Testing scanner functionality...")
    
    scanner = BarcodeScanner()
    
    # Test parsing different formats
    test_data = [
        # JSON format
        '{"name": "Test User", "company": "Test Company", "email": "<EMAIL>"}',
        
        # Delimited format
        'Jane Doe|ABC Corporation|<EMAIL>|Manager',
        
        # Simple text
        'Simple Name',
        
        # vCard format
        '''BEGIN:VCARD
VERSION:3.0
FN:Bob Wilson
ORG:Wilson Industries
EMAIL:<EMAIL>
TEL:******-0123
TITLE:CEO
END:VCARD''',
        
        # URL format
        'https://example.com/visitor?name=Alice%20Brown&company=Brown%20LLC&email=<EMAIL>'
    ]
    
    for i, data in enumerate(test_data):
        print(f"\nTest {i+1}: {data[:50]}...")
        parsed = scanner.parse_scan_data(data)
        print(f"Parsed: {parsed}")

def create_sample_images():
    """Create placeholder images for the interface"""
    try:
        from PIL import Image, ImageDraw, ImageFont
        
        # Create images directory
        os.makedirs('static/images', exist_ok=True)
        
        # Create logo placeholder
        logo_img = Image.new('RGB', (200, 80), color='#0d6efd')
        draw = ImageDraw.Draw(logo_img)
        
        try:
            # Try to use a system font
            font = ImageFont.truetype("arial.ttf", 24)
        except:
            font = ImageFont.load_default()
        
        draw.text((10, 25), "LASER DEMO", fill='white', font=font)
        logo_img.save('static/images/logo.png')
        print("Created logo placeholder")
        
        # Create laser system images
        for i in range(1, 3):
            laser_img = Image.new('RGB', (300, 200), color='#6c757d')
            draw = ImageDraw.Draw(laser_img)
            draw.text((80, 90), f"Laser System {i}", fill='white', font=font)
            laser_img.save(f'static/images/laser{i}.jpg')
            print(f"Created laser{i}.jpg placeholder")
            
    except ImportError:
        print("PIL not available, skipping image creation")
        print("You can add your own images to static/images/:")
        print("- logo.png (company logo)")
        print("- laser1.jpg, laser2.jpg (product images)")

def print_startup_info():
    """Print startup information"""
    print("=" * 60)
    print("LASER DEMONSTRATION SYSTEM")
    print("=" * 60)
    print()
    print("Setup completed! To start the system:")
    print()
    print("1. Install dependencies:")
    print("   pip install -r requirements.txt")
    print()
    print("2. Configure EZcad3 connection in .env file")
    print()
    print("3. Start the application:")
    print("   python app.py")
    print()
    print("4. Open web interface:")
    print("   http://localhost:5000")
    print()
    print("Sample QR codes created in 'sample_qr_codes/' directory")
    print("Use these to test the scanning functionality")
    print()
    print("=" * 60)

if __name__ == "__main__":
    print("Setting up Laser Demonstration System...")
    print()
    
    # Create sample QR codes
    create_sample_qr_codes()
    
    # Test scanner
    test_scanner()
    
    # Create sample images
    create_sample_images()
    
    # Print startup info
    print_startup_info()
