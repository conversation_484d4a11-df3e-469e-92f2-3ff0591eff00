"""
EZcad3 TCP Client for Laser Control
Handles communication with EZcad3 laser marking software via TCP/IP
"""

import socket
import time
import threading
import logging
from typing import Optional, Dict, Any, Callable
from enum import Enum
from dataclasses import dataclass
from config import config

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class LaserStatus(Enum):
    """Laser system status enumeration"""
    DISCONNECTED = "disconnected"
    CONNECTED = "connected"
    IDLE = "idle"
    MARKING = "marking"
    PAUSED = "paused"
    ERROR = "error"
    DOOR_OPEN = "door_open"
    DOOR_CLOSED = "door_closed"

class LaserCommand(Enum):
    """EZcad3 TCP command enumeration"""
    # Connection commands
    CONNECT = "CONNECT"
    DISCONNECT = "DISCONNECT"
    
    # File operations
    LOAD_FILE = "LOADFILE"
    SAVE_FILE = "SAVEFILE"
    
    # Marking operations
    START_MARK = "STARTMARK"
    STOP_MARK = "STOPMARK"
    PAUSE_MARK = "PAUSEMARK"
    RESUME_MARK = "RESUMEMARK"
    
    # Parameter operations
    SET_POWER = "SETPOWER"
    SET_SPEED = "SETSPEED"
    SET_FREQUENCY = "SETFREQ"
    
    # Status queries
    GET_STATUS = "GETSTATUS"
    GET_PROGRESS = "GETPROGRESS"
    
    # Safety operations
    OPEN_DOOR = "OPENDOOR"
    CLOSE_DOOR = "CLOSEDOOR"
    EMERGENCY_STOP = "ESTOP"
    
    # Template operations
    SET_TEXT = "SETTEXT"
    SET_VARIABLE = "SETVAR"

@dataclass
class LaserResponse:
    """Response from EZcad3 system"""
    success: bool
    message: str
    data: Optional[Dict[str, Any]] = None
    timestamp: float = None
    
    def __post_init__(self):
        if self.timestamp is None:
            self.timestamp = time.time()

class EZcad3Client:
    """TCP client for EZcad3 laser control system"""
    
    def __init__(self, host: str = None, port: int = None):
        self.host = host or config.ezcad3.host
        self.port = port or config.ezcad3.port
        self.timeout = config.ezcad3.timeout
        
        self._socket: Optional[socket.socket] = None
        self._connected = False
        self._status = LaserStatus.DISCONNECTED
        self._lock = threading.Lock()
        self._status_callbacks: list[Callable] = []
        
        # Status monitoring
        self._monitor_thread: Optional[threading.Thread] = None
        self._monitor_running = False
        
        logger.info(f"EZcad3Client initialized for {self.host}:{self.port}")
    
    def add_status_callback(self, callback: Callable[[LaserStatus, Dict], None]):
        """Add callback for status changes"""
        self._status_callbacks.append(callback)
    
    def _notify_status_change(self, status: LaserStatus, data: Dict = None):
        """Notify all callbacks of status change"""
        self._status = status
        for callback in self._status_callbacks:
            try:
                callback(status, data or {})
            except Exception as e:
                logger.error(f"Status callback error: {e}")
    
    def connect(self) -> LaserResponse:
        """Connect to EZcad3 TCP server"""
        with self._lock:
            if self._connected:
                return LaserResponse(True, "Already connected")
            
            try:
                self._socket = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
                self._socket.settimeout(self.timeout)
                self._socket.connect((self.host, self.port))
                
                # Send initial connection command
                response = self._send_command(LaserCommand.CONNECT)
                
                if response.success:
                    self._connected = True
                    self._start_status_monitor()
                    self._notify_status_change(LaserStatus.CONNECTED)
                    logger.info("Connected to EZcad3")
                else:
                    self._socket.close()
                    self._socket = None
                
                return response
                
            except Exception as e:
                logger.error(f"Connection failed: {e}")
                if self._socket:
                    self._socket.close()
                    self._socket = None
                return LaserResponse(False, f"Connection failed: {str(e)}")
    
    def disconnect(self) -> LaserResponse:
        """Disconnect from EZcad3 TCP server"""
        with self._lock:
            if not self._connected:
                return LaserResponse(True, "Already disconnected")
            
            try:
                self._stop_status_monitor()
                
                if self._socket:
                    self._send_command(LaserCommand.DISCONNECT)
                    self._socket.close()
                    self._socket = None
                
                self._connected = False
                self._notify_status_change(LaserStatus.DISCONNECTED)
                logger.info("Disconnected from EZcad3")
                
                return LaserResponse(True, "Disconnected successfully")
                
            except Exception as e:
                logger.error(f"Disconnect error: {e}")
                return LaserResponse(False, f"Disconnect error: {str(e)}")
    
    def _send_command(self, command: LaserCommand, params: str = "") -> LaserResponse:
        """Send command to EZcad3 and get response"""
        if not self._socket:
            return LaserResponse(False, "Not connected")
        
        try:
            # Format command
            cmd_str = f"{command.value}"
            if params:
                cmd_str += f" {params}"
            cmd_str += "\r\n"
            
            # Send command
            self._socket.send(cmd_str.encode('utf-8'))
            logger.debug(f"Sent command: {cmd_str.strip()}")
            
            # Receive response
            response_data = self._socket.recv(1024).decode('utf-8').strip()
            logger.debug(f"Received response: {response_data}")
            
            # Parse response
            return self._parse_response(response_data)
            
        except socket.timeout:
            logger.error("Command timeout")
            return LaserResponse(False, "Command timeout")
        except Exception as e:
            logger.error(f"Command error: {e}")
            return LaserResponse(False, f"Command error: {str(e)}")
    
    def _parse_response(self, response: str) -> LaserResponse:
        """Parse response from EZcad3"""
        try:
            # Basic response parsing - adapt based on actual EZcad3 protocol
            if response.startswith("OK"):
                return LaserResponse(True, "Command successful", {"raw": response})
            elif response.startswith("ERROR"):
                return LaserResponse(False, response, {"raw": response})
            else:
                # Parse status or data responses
                return LaserResponse(True, "Response received", {"raw": response})
                
        except Exception as e:
            logger.error(f"Response parsing error: {e}")
            return LaserResponse(False, f"Parse error: {str(e)}")
    
    def start_marking(self) -> LaserResponse:
        """Start the marking process"""
        logger.info("Starting marking process")
        response = self._send_command(LaserCommand.START_MARK)
        if response.success:
            self._notify_status_change(LaserStatus.MARKING)
        return response
    
    def stop_marking(self) -> LaserResponse:
        """Stop the marking process"""
        logger.info("Stopping marking process")
        response = self._send_command(LaserCommand.STOP_MARK)
        if response.success:
            self._notify_status_change(LaserStatus.IDLE)
        return response
    
    def emergency_stop(self) -> LaserResponse:
        """Emergency stop - immediate halt"""
        logger.warning("Emergency stop activated")
        response = self._send_command(LaserCommand.EMERGENCY_STOP)
        if response.success:
            self._notify_status_change(LaserStatus.ERROR, {"emergency_stop": True})
        return response
    
    def load_template(self, filename: str) -> LaserResponse:
        """Load EZcad3 template file"""
        logger.info(f"Loading template: {filename}")
        return self._send_command(LaserCommand.LOAD_FILE, filename)
    
    def set_text_variable(self, variable_name: str, value: str) -> LaserResponse:
        """Set text variable in loaded template"""
        logger.info(f"Setting variable {variable_name} = {value}")
        params = f"{variable_name} {value}"
        return self._send_command(LaserCommand.SET_VARIABLE, params)
    
    def get_status(self) -> LaserResponse:
        """Get current system status"""
        return self._send_command(LaserCommand.GET_STATUS)
    
    def get_progress(self) -> LaserResponse:
        """Get marking progress"""
        return self._send_command(LaserCommand.GET_PROGRESS)
    
    def close_door(self) -> LaserResponse:
        """Close safety door"""
        logger.info("Closing safety door")
        response = self._send_command(LaserCommand.CLOSE_DOOR)
        if response.success:
            self._notify_status_change(LaserStatus.DOOR_CLOSED)
        return response
    
    def open_door(self) -> LaserResponse:
        """Open safety door"""
        logger.info("Opening safety door")
        response = self._send_command(LaserCommand.OPEN_DOOR)
        if response.success:
            self._notify_status_change(LaserStatus.DOOR_OPEN)
        return response
    
    def _start_status_monitor(self):
        """Start status monitoring thread"""
        if self._monitor_thread and self._monitor_thread.is_alive():
            return
        
        self._monitor_running = True
        self._monitor_thread = threading.Thread(target=self._status_monitor_loop)
        self._monitor_thread.daemon = True
        self._monitor_thread.start()
        logger.info("Status monitor started")
    
    def _stop_status_monitor(self):
        """Stop status monitoring thread"""
        self._monitor_running = False
        if self._monitor_thread:
            self._monitor_thread.join(timeout=5)
        logger.info("Status monitor stopped")
    
    def _status_monitor_loop(self):
        """Status monitoring loop"""
        while self._monitor_running and self._connected:
            try:
                # Query status periodically
                response = self.get_status()
                if response.success:
                    # Parse status and update accordingly
                    # This would be customized based on actual EZcad3 responses
                    pass
                
                time.sleep(1)  # Check status every second
                
            except Exception as e:
                logger.error(f"Status monitor error: {e}")
                time.sleep(5)  # Wait longer on error
    
    @property
    def connected(self) -> bool:
        """Check if connected to EZcad3"""
        return self._connected
    
    @property
    def status(self) -> LaserStatus:
        """Get current laser status"""
        return self._status
    
    def __enter__(self):
        """Context manager entry"""
        self.connect()
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        """Context manager exit"""
        self.disconnect()

# Global client instance
laser_client = EZcad3Client()
