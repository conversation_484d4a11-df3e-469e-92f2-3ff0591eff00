"""
Template Management System
Handles EZcad3 template files and variable substitution
"""

import os
import json
import time
import logging
from typing import Dict, List, Any, Optional
from datetime import datetime
from config import config

logger = logging.getLogger(__name__)

class TemplateManager:
    """Manages EZcad3 templates and variable substitution"""
    
    def __init__(self):
        self.templates_dir = config.template.templates_dir
        self.default_template = config.template.default_template
        self.variable_prefix = config.template.variable_prefix
        self.variable_suffix = config.template.variable_suffix
        
        # Template metadata cache
        self._template_cache: Dict[str, Dict] = {}
        
        logger.info("TemplateManager initialized")
    
    def initialize(self):
        """Initialize template manager and scan for templates"""
        try:
            # Create templates directory if it doesn't exist
            os.makedirs(self.templates_dir, exist_ok=True)
            
            # Scan for templates
            self._scan_templates()
            
            # Create sample templates if none exist
            if not self._template_cache:
                self._create_sample_templates()
            
            logger.info(f"Template manager initialized with {len(self._template_cache)} templates")
            
        except Exception as e:
            logger.error(f"Template manager initialization error: {e}")
    
    def _scan_templates(self):
        """Scan templates directory for EZcad3 files"""
        self._template_cache.clear()
        
        try:
            if not os.path.exists(self.templates_dir):
                return
            
            for filename in os.listdir(self.templates_dir):
                if filename.endswith('.ezd') or filename.endswith('.json'):
                    template_path = os.path.join(self.templates_dir, filename)
                    
                    # Load template metadata
                    metadata = self._load_template_metadata(template_path)
                    if metadata:
                        self._template_cache[filename] = metadata
                        logger.info(f"Loaded template: {filename}")
            
        except Exception as e:
            logger.error(f"Template scan error: {e}")
    
    def _load_template_metadata(self, template_path: str) -> Optional[Dict]:
        """Load template metadata"""
        try:
            filename = os.path.basename(template_path)
            
            # Check for companion metadata file
            metadata_path = template_path.replace('.ezd', '.json')
            
            if os.path.exists(metadata_path):
                with open(metadata_path, 'r', encoding='utf-8') as f:
                    metadata = json.load(f)
            else:
                # Create default metadata
                metadata = self._create_default_metadata(filename)
            
            # Add file information
            stat = os.stat(template_path)
            metadata['file_info'] = {
                'path': template_path,
                'size': stat.st_size,
                'modified': stat.st_mtime,
                'created': stat.st_ctime
            }
            
            return metadata
            
        except Exception as e:
            logger.error(f"Metadata load error for {template_path}: {e}")
            return None
    
    def _create_default_metadata(self, filename: str) -> Dict:
        """Create default metadata for template"""
        return {
            'name': filename.replace('.ezd', '').replace('_', ' ').title(),
            'description': f'Template: {filename}',
            'variables': self._extract_default_variables(),
            'category': 'General',
            'preview_image': None,
            'created': time.time(),
            'version': '1.0'
        }
    
    def _extract_default_variables(self) -> List[Dict]:
        """Extract default template variables"""
        return [
            {
                'name': 'name',
                'display_name': 'Name',
                'type': 'text',
                'required': True,
                'default': 'John Doe',
                'max_length': 50
            },
            {
                'name': 'company',
                'display_name': 'Company',
                'type': 'text',
                'required': False,
                'default': 'Company Name',
                'max_length': 50
            },
            {
                'name': 'email',
                'display_name': 'Email',
                'type': 'email',
                'required': False,
                'default': '<EMAIL>',
                'max_length': 100
            },
            {
                'name': 'date',
                'display_name': 'Date',
                'type': 'date',
                'required': False,
                'default': datetime.now().strftime('%Y-%m-%d'),
                'format': '%Y-%m-%d'
            },
            {
                'name': 'time',
                'display_name': 'Time',
                'type': 'time',
                'required': False,
                'default': datetime.now().strftime('%H:%M'),
                'format': '%H:%M'
            }
        ]
    
    def get_available_templates(self) -> List[Dict]:
        """Get list of available templates"""
        templates = []
        
        for filename, metadata in self._template_cache.items():
            template_info = {
                'filename': filename,
                'name': metadata.get('name', filename),
                'description': metadata.get('description', ''),
                'category': metadata.get('category', 'General'),
                'variables': metadata.get('variables', []),
                'preview_image': metadata.get('preview_image'),
                'file_size': metadata.get('file_info', {}).get('size', 0),
                'modified': metadata.get('file_info', {}).get('modified', 0)
            }
            templates.append(template_info)
        
        # Sort by name
        templates.sort(key=lambda x: x['name'])
        
        return templates
    
    def get_template_info(self, filename: str) -> Optional[Dict]:
        """Get detailed information about a specific template"""
        return self._template_cache.get(filename)
    
    def prepare_template_variables(self, filename: str, scan_data: Dict[str, Any]) -> Dict[str, str]:
        """
        Prepare template variables from scan data
        
        Args:
            filename: Template filename
            scan_data: Scanned visitor data
            
        Returns:
            Dictionary of variable name -> value mappings
        """
        template_info = self.get_template_info(filename)
        if not template_info:
            logger.error(f"Template not found: {filename}")
            return {}
        
        variables = {}
        template_variables = template_info.get('variables', [])
        
        for var_info in template_variables:
            var_name = var_info['name']
            var_type = var_info.get('type', 'text')
            default_value = var_info.get('default', '')
            
            # Get value from scan data or use default
            if var_name in scan_data and scan_data[var_name]:
                value = str(scan_data[var_name])
            else:
                value = self._generate_default_value(var_type, default_value)
            
            # Apply formatting if specified
            if var_type == 'date' and 'format' in var_info:
                try:
                    if isinstance(value, str) and value != default_value:
                        # Try to parse and reformat
                        dt = datetime.fromisoformat(value.replace('Z', '+00:00'))
                        value = dt.strftime(var_info['format'])
                except:
                    value = datetime.now().strftime(var_info['format'])
            
            elif var_type == 'time' and 'format' in var_info:
                try:
                    if isinstance(value, str) and value != default_value:
                        dt = datetime.fromisoformat(value.replace('Z', '+00:00'))
                        value = dt.strftime(var_info['format'])
                except:
                    value = datetime.now().strftime(var_info['format'])
            
            # Apply length limits
            max_length = var_info.get('max_length')
            if max_length and len(value) > max_length:
                value = value[:max_length]
            
            variables[var_name] = value
        
        logger.info(f"Prepared variables for {filename}: {list(variables.keys())}")
        return variables
    
    def _generate_default_value(self, var_type: str, default_value: str) -> str:
        """Generate default value based on variable type"""
        if var_type == 'date':
            return datetime.now().strftime('%Y-%m-%d')
        elif var_type == 'time':
            return datetime.now().strftime('%H:%M')
        elif var_type == 'datetime':
            return datetime.now().strftime('%Y-%m-%d %H:%M')
        else:
            return default_value
    
    def validate_template_variables(self, filename: str, variables: Dict[str, str]) -> Dict[str, Any]:
        """
        Validate template variables
        
        Args:
            filename: Template filename
            variables: Variable values to validate
            
        Returns:
            Validation result with errors if any
        """
        result = {
            'valid': True,
            'errors': [],
            'warnings': []
        }
        
        template_info = self.get_template_info(filename)
        if not template_info:
            result['valid'] = False
            result['errors'].append(f"Template not found: {filename}")
            return result
        
        template_variables = template_info.get('variables', [])
        
        for var_info in template_variables:
            var_name = var_info['name']
            required = var_info.get('required', False)
            max_length = var_info.get('max_length')
            var_type = var_info.get('type', 'text')
            
            # Check if required variable is present
            if required and (var_name not in variables or not variables[var_name]):
                result['valid'] = False
                result['errors'].append(f"Required variable '{var_name}' is missing")
                continue
            
            if var_name in variables:
                value = variables[var_name]
                
                # Check length limits
                if max_length and len(value) > max_length:
                    result['warnings'].append(f"Variable '{var_name}' exceeds maximum length ({max_length})")
                
                # Type-specific validation
                if var_type == 'email' and value:
                    import re
                    email_pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
                    if not re.match(email_pattern, value):
                        result['warnings'].append(f"Variable '{var_name}' is not a valid email address")
        
        return result
    
    def _create_sample_templates(self):
        """Create sample templates for demonstration"""
        try:
            # Business card template metadata
            business_card_meta = {
                'name': 'Business Card',
                'description': 'Standard business card template with name, company, and contact info',
                'category': 'Business',
                'variables': [
                    {
                        'name': 'name',
                        'display_name': 'Full Name',
                        'type': 'text',
                        'required': True,
                        'default': 'John Doe',
                        'max_length': 50
                    },
                    {
                        'name': 'company',
                        'display_name': 'Company Name',
                        'type': 'text',
                        'required': False,
                        'default': 'Your Company',
                        'max_length': 50
                    },
                    {
                        'name': 'email',
                        'display_name': 'Email Address',
                        'type': 'email',
                        'required': False,
                        'default': '<EMAIL>',
                        'max_length': 100
                    }
                ],
                'created': time.time(),
                'version': '1.0'
            }
            
            # Name tag template metadata
            name_tag_meta = {
                'name': 'Name Tag',
                'description': 'Simple name tag for exhibitions and events',
                'category': 'Exhibition',
                'variables': [
                    {
                        'name': 'name',
                        'display_name': 'Name',
                        'type': 'text',
                        'required': True,
                        'default': 'Visitor Name',
                        'max_length': 30
                    },
                    {
                        'name': 'company',
                        'display_name': 'Company',
                        'type': 'text',
                        'required': False,
                        'default': 'Company Name',
                        'max_length': 40
                    }
                ],
                'created': time.time(),
                'version': '1.0'
            }
            
            # Save metadata files
            templates = [
                ('business_card.json', business_card_meta),
                ('name_tag.json', name_tag_meta)
            ]
            
            for filename, metadata in templates:
                filepath = os.path.join(self.templates_dir, filename)
                with open(filepath, 'w', encoding='utf-8') as f:
                    json.dump(metadata, f, indent=2)
                
                # Add to cache
                self._template_cache[filename.replace('.json', '.ezd')] = metadata
                
                logger.info(f"Created sample template: {filename}")
            
        except Exception as e:
            logger.error(f"Sample template creation error: {e}")
    
    def refresh_templates(self):
        """Refresh template cache by rescanning directory"""
        logger.info("Refreshing template cache")
        self._scan_templates()
    
    def get_template_categories(self) -> List[str]:
        """Get list of template categories"""
        categories = set()
        for metadata in self._template_cache.values():
            categories.add(metadata.get('category', 'General'))
        
        return sorted(list(categories))
