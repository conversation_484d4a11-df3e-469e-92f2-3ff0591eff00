"""
Configuration settings for the Laser Demonstration System
"""
import os
from dataclasses import dataclass
from typing import Dict, Any

@dataclass
class EZcad3Config:
    """EZcad3 TCP connection configuration"""
    host: str = "localhost"
    port: int = 8888
    timeout: int = 30
    reconnect_attempts: int = 3
    reconnect_delay: int = 5
    mock_mode: bool = False

@dataclass
class WebServerConfig:
    """Web server configuration"""
    host: str = "0.0.0.0"
    port: int = 5000
    debug: bool = False
    secret_key: str = "laser-demo-secret-key-change-in-production"

@dataclass
class LaserConfig:
    """Laser operation configuration"""
    safety_door_timeout: int = 10  # seconds
    marking_timeout: int = 300     # seconds
    auto_mode_enabled: bool = True
    emergency_stop_enabled: bool = True

@dataclass
class ScannerConfig:
    """Barcode/QR scanner configuration"""
    camera_index: int = 0
    scan_timeout: int = 30
    supported_formats: list = None
    
    def __post_init__(self):
        if self.supported_formats is None:
            self.supported_formats = ['CODE128', 'QR_CODE', 'EAN13', 'EAN8', 'CODE39']

@dataclass
class TemplateConfig:
    """Template management configuration"""
    templates_dir: str = "templates"
    default_template: str = "business_card.ezd"
    variable_prefix: str = "{{"
    variable_suffix: str = "}}"

class Config:
    """Main configuration class"""
    
    def __init__(self):
        self.ezcad3 = EZcad3Config()
        self.web_server = WebServerConfig()
        self.laser = LaserConfig()
        self.scanner = ScannerConfig()
        self.template = TemplateConfig()
        
        # Load environment variables
        self._load_from_env()
    
    def _load_from_env(self):
        """Load configuration from environment variables"""
        # EZcad3 settings
        self.ezcad3.host = os.getenv('EZCAD3_HOST', self.ezcad3.host)
        self.ezcad3.port = int(os.getenv('EZCAD3_PORT', self.ezcad3.port))
        self.ezcad3.mock_mode = os.getenv('MOCK_MODE', 'False').lower() == 'true'
        
        # Web server settings
        self.web_server.host = os.getenv('WEB_HOST', self.web_server.host)
        self.web_server.port = int(os.getenv('WEB_PORT', self.web_server.port))
        self.web_server.debug = os.getenv('DEBUG', 'False').lower() == 'true'
        
        # Template settings
        self.template.templates_dir = os.getenv('TEMPLATES_DIR', self.template.templates_dir)
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert configuration to dictionary"""
        return {
            'ezcad3': {
                'host': self.ezcad3.host,
                'port': self.ezcad3.port,
                'timeout': self.ezcad3.timeout
            },
            'web_server': {
                'host': self.web_server.host,
                'port': self.web_server.port,
                'debug': self.web_server.debug
            },
            'laser': {
                'safety_door_timeout': self.laser.safety_door_timeout,
                'marking_timeout': self.laser.marking_timeout,
                'auto_mode_enabled': self.laser.auto_mode_enabled
            },
            'scanner': {
                'camera_index': self.scanner.camera_index,
                'scan_timeout': self.scanner.scan_timeout,
                'supported_formats': self.scanner.supported_formats
            },
            'template': {
                'templates_dir': self.template.templates_dir,
                'default_template': self.template.default_template
            }
        }

# Global configuration instance
config = Config()
