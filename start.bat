@echo off
REM Laser Demonstration System - Windows Startup Script

echo ========================================
echo LASER DEMONSTRATION SYSTEM
echo ========================================
echo.

REM Check if Python is installed
python --version >nul 2>&1
if errorlevel 1 (
    echo ERROR: Python is not installed or not in PATH
    echo Please install Python 3.8 or higher
    pause
    exit /b 1
)

echo Python found: 
python --version

REM Check if virtual environment exists
if exist "venv" (
    echo Activating virtual environment...
    call venv\Scripts\activate.bat
) else (
    echo No virtual environment found. Creating one...
    python -m venv venv
    call venv\Scripts\activate.bat
    echo Installing dependencies...
    pip install -r requirements.txt
)

REM Run system validation
echo.
echo Running system validation...
python validate_system.py
if errorlevel 1 (
    echo.
    echo System validation failed. Please check the errors above.
    pause
    exit /b 1
)

REM Start the application
echo.
echo Starting Laser Demonstration System...
echo.
echo Web interface will be available at:
echo   http://localhost:5000
echo.
echo Press Ctrl+C to stop the server
echo.

python app.py

pause
