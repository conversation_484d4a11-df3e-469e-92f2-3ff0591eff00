/**
 * Laser Demonstration System - Frontend JavaScript Application
 * Handles UI interactions, WebSocket communication, and camera integration
 */

class LaserDemoApp {
    constructor() {
        this.socket = null;
        this.systemState = {};
        this.cameraStream = null;
        this.isScanning = false;
        
        this.init();
    }
    
    init() {
        console.log('Initializing Laser Demo App...');
        
        // Initialize Socket.IO connection
        this.initSocket();
        
        // Bind event handlers
        this.bindEvents();
        
        // Load templates
        this.loadTemplates();
        
        // Initialize UI state
        this.updateUI();
        
        console.log('Laser Demo App initialized');
    }
    
    initSocket() {
        this.socket = io();
        
        this.socket.on('connect', () => {
            console.log('Connected to server');
            this.showMessage('Connected to server', 'success');
        });
        
        this.socket.on('disconnect', () => {
            console.log('Disconnected from server');
            this.showMessage('Disconnected from server', 'warning');
        });
        
        this.socket.on('system_state_update', (data) => {
            console.log('System state update:', data);
            Object.assign(this.systemState, data);
            this.updateUI();
        });
        
        this.socket.on('system_state', (state) => {
            console.log('Full system state:', state);
            this.systemState = state;
            this.updateUI();
        });
        
        this.socket.on('auto_sequence_complete', (data) => {
            console.log('AUTO sequence complete:', data);
            if (data.success) {
                this.showMessage('AUTO sequence completed successfully!', 'success');
            } else {
                this.showMessage('AUTO sequence failed', 'danger');
            }
        });
    }
    
    bindEvents() {
        // Connection controls
        document.getElementById('connect-btn').addEventListener('click', () => this.connectLaser());
        document.getElementById('disconnect-btn').addEventListener('click', () => this.disconnectLaser());
        
        // Laser operation controls
        document.getElementById('auto-btn').addEventListener('click', () => this.executeAutoSequence());
        document.getElementById('start-btn').addEventListener('click', () => this.startMarking());
        document.getElementById('stop-btn').addEventListener('click', () => this.stopMarking());
        
        // Door controls
        document.getElementById('close-door-btn').addEventListener('click', () => this.closeDoor());
        document.getElementById('open-door-btn').addEventListener('click', () => this.openDoor());
        
        // Emergency stop
        document.getElementById('emergency-stop-btn').addEventListener('click', () => this.emergencyStop());
        
        // Scanner controls
        document.getElementById('scan-camera-btn').addEventListener('click', () => this.openCameraModal());
        document.getElementById('manual-entry-btn').addEventListener('click', () => this.toggleManualEntry());
        document.getElementById('process-manual-btn').addEventListener('click', () => this.processManualEntry());
        document.getElementById('capture-btn').addEventListener('click', () => this.captureAndScan());
        
        // Template selection
        document.getElementById('template-select').addEventListener('change', (e) => this.selectTemplate(e.target.value));
        
        // Modal events
        const cameraModal = document.getElementById('cameraModal');
        cameraModal.addEventListener('shown.bs.modal', () => this.startCamera());
        cameraModal.addEventListener('hidden.bs.modal', () => this.stopCamera());
    }
    
    async connectLaser() {
        try {
            this.setButtonLoading('connect-btn', true);
            
            const response = await fetch('/api/connect', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' }
            });
            
            const result = await response.json();
            
            if (result.success) {
                this.showMessage('Connected to laser system', 'success');
            } else {
                this.showMessage(`Connection failed: ${result.message}`, 'danger');
            }
        } catch (error) {
            console.error('Connect error:', error);
            this.showMessage('Connection error', 'danger');
        } finally {
            this.setButtonLoading('connect-btn', false);
        }
    }
    
    async disconnectLaser() {
        try {
            this.setButtonLoading('disconnect-btn', true);
            
            const response = await fetch('/api/disconnect', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' }
            });
            
            const result = await response.json();
            
            if (result.success) {
                this.showMessage('Disconnected from laser system', 'info');
            } else {
                this.showMessage(`Disconnect failed: ${result.message}`, 'warning');
            }
        } catch (error) {
            console.error('Disconnect error:', error);
            this.showMessage('Disconnect error', 'danger');
        } finally {
            this.setButtonLoading('disconnect-btn', false);
        }
    }
    
    async executeAutoSequence() {
        if (!this.systemState.current_template) {
            this.showMessage('Please select a template first', 'warning');
            return;
        }
        
        if (!this.systemState.scan_data) {
            this.showMessage('Please scan visitor information first', 'warning');
            return;
        }
        
        try {
            this.setButtonLoading('auto-btn', true);
            
            const response = await fetch('/api/laser/auto', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' }
            });
            
            const result = await response.json();
            
            if (result.success) {
                this.showMessage('AUTO sequence started', 'info');
            } else {
                this.showMessage(`AUTO sequence failed: ${result.message}`, 'danger');
            }
        } catch (error) {
            console.error('AUTO sequence error:', error);
            this.showMessage('AUTO sequence error', 'danger');
        } finally {
            this.setButtonLoading('auto-btn', false);
        }
    }
    
    async startMarking() {
        try {
            this.setButtonLoading('start-btn', true);
            
            const response = await fetch('/api/laser/start', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' }
            });
            
            const result = await response.json();
            
            if (result.success) {
                this.showMessage('Marking started', 'success');
            } else {
                this.showMessage(`Start failed: ${result.message}`, 'danger');
            }
        } catch (error) {
            console.error('Start marking error:', error);
            this.showMessage('Start marking error', 'danger');
        } finally {
            this.setButtonLoading('start-btn', false);
        }
    }
    
    async stopMarking() {
        try {
            this.setButtonLoading('stop-btn', true);
            
            const response = await fetch('/api/laser/stop', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' }
            });
            
            const result = await response.json();
            
            if (result.success) {
                this.showMessage('Marking stopped', 'info');
            } else {
                this.showMessage(`Stop failed: ${result.message}`, 'warning');
            }
        } catch (error) {
            console.error('Stop marking error:', error);
            this.showMessage('Stop marking error', 'danger');
        } finally {
            this.setButtonLoading('stop-btn', false);
        }
    }
    
    async closeDoor() {
        try {
            this.setButtonLoading('close-door-btn', true);
            
            const response = await fetch('/api/door/close', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' }
            });
            
            const result = await response.json();
            
            if (result.success) {
                this.showMessage('Door closing...', 'info');
            } else {
                this.showMessage(`Door close failed: ${result.message}`, 'warning');
            }
        } catch (error) {
            console.error('Close door error:', error);
            this.showMessage('Close door error', 'danger');
        } finally {
            this.setButtonLoading('close-door-btn', false);
        }
    }
    
    async openDoor() {
        try {
            this.setButtonLoading('open-door-btn', true);
            
            const response = await fetch('/api/door/open', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' }
            });
            
            const result = await response.json();
            
            if (result.success) {
                this.showMessage('Door opening...', 'info');
            } else {
                this.showMessage(`Door open failed: ${result.message}`, 'warning');
            }
        } catch (error) {
            console.error('Open door error:', error);
            this.showMessage('Open door error', 'danger');
        } finally {
            this.setButtonLoading('open-door-btn', false);
        }
    }
    
    async emergencyStop() {
        if (!confirm('Are you sure you want to activate EMERGENCY STOP?')) {
            return;
        }

        try {
            const response = await fetch('/api/laser/emergency_stop', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' }
            });

            const result = await response.json();

            if (result.success) {
                this.showMessage('EMERGENCY STOP ACTIVATED!', 'danger');
            } else {
                this.showMessage(`Emergency stop failed: ${result.message}`, 'danger');
            }
        } catch (error) {
            console.error('Emergency stop error:', error);
            this.showMessage('Emergency stop error', 'danger');
        }
    }

    // Scanner Methods
    openCameraModal() {
        const modal = new bootstrap.Modal(document.getElementById('cameraModal'));
        modal.show();
    }

    toggleManualEntry() {
        const form = document.getElementById('manual-entry-form');
        const btn = document.getElementById('manual-entry-btn');

        if (form.classList.contains('d-none')) {
            form.classList.remove('d-none');
            btn.textContent = 'Cancel Manual Entry';
            btn.classList.remove('btn-outline-success');
            btn.classList.add('btn-outline-secondary');
        } else {
            form.classList.add('d-none');
            btn.textContent = 'Manual Entry';
            btn.classList.remove('btn-outline-secondary');
            btn.classList.add('btn-outline-success');
        }
    }

    async processManualEntry() {
        const name = document.getElementById('visitor-name').value.trim();
        const company = document.getElementById('visitor-company').value.trim();
        const email = document.getElementById('visitor-email').value.trim();

        if (!name) {
            this.showMessage('Please enter visitor name', 'warning');
            return;
        }

        const scanData = {
            method: 'manual',
            data: JSON.stringify({
                name: name,
                company: company,
                email: email,
                type: 'manual_entry'
            })
        };

        try {
            const response = await fetch('/api/scan', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify(scanData)
            });

            const result = await response.json();

            if (result.success) {
                this.showMessage('Visitor information processed', 'success');
                this.displayVisitorInfo(result.data);
                this.toggleManualEntry(); // Hide form
                this.clearManualForm();
            } else {
                this.showMessage(`Processing failed: ${result.message}`, 'danger');
            }
        } catch (error) {
            console.error('Manual entry error:', error);
            this.showMessage('Manual entry error', 'danger');
        }
    }

    clearManualForm() {
        document.getElementById('visitor-name').value = '';
        document.getElementById('visitor-company').value = '';
        document.getElementById('visitor-email').value = '';
    }

    async startCamera() {
        try {
            this.cameraStream = await navigator.mediaDevices.getUserMedia({
                video: {
                    width: { ideal: 640 },
                    height: { ideal: 480 },
                    facingMode: 'environment' // Use back camera on mobile
                }
            });

            const video = document.getElementById('camera-video');
            video.srcObject = this.cameraStream;

            console.log('Camera started');
        } catch (error) {
            console.error('Camera error:', error);
            this.showMessage('Camera access denied or not available', 'danger');
        }
    }

    stopCamera() {
        if (this.cameraStream) {
            this.cameraStream.getTracks().forEach(track => track.stop());
            this.cameraStream = null;
            console.log('Camera stopped');
        }
    }

    async captureAndScan() {
        if (!this.cameraStream) {
            this.showMessage('Camera not available', 'danger');
            return;
        }

        try {
            this.setButtonLoading('capture-btn', true);

            const video = document.getElementById('camera-video');
            const canvas = document.getElementById('camera-canvas');
            const ctx = canvas.getContext('2d');

            // Set canvas size to match video
            canvas.width = video.videoWidth;
            canvas.height = video.videoHeight;

            // Draw video frame to canvas
            ctx.drawImage(video, 0, 0);

            // Convert canvas to blob
            canvas.toBlob(async (blob) => {
                const formData = new FormData();
                formData.append('image', blob, 'capture.jpg');
                formData.append('method', 'camera');

                try {
                    const response = await fetch('/api/scan', {
                        method: 'POST',
                        body: formData
                    });

                    const result = await response.json();

                    if (result.success) {
                        this.showMessage('QR/Barcode scanned successfully!', 'success');
                        this.displayVisitorInfo(result.data);

                        // Close modal
                        const modal = bootstrap.Modal.getInstance(document.getElementById('cameraModal'));
                        modal.hide();
                    } else {
                        this.showMessage(`Scan failed: ${result.message}`, 'warning');
                    }
                } catch (error) {
                    console.error('Scan error:', error);
                    this.showMessage('Scan processing error', 'danger');
                }
            }, 'image/jpeg', 0.8);

        } catch (error) {
            console.error('Capture error:', error);
            this.showMessage('Image capture error', 'danger');
        } finally {
            this.setButtonLoading('capture-btn', false);
        }
    }

    displayVisitorInfo(data) {
        document.getElementById('display-name').textContent = data.name || '-';
        document.getElementById('display-company').textContent = data.company || '-';
        document.getElementById('display-email').textContent = data.email || '-';

        document.getElementById('visitor-info').style.display = 'block';
        document.getElementById('visitor-info').classList.add('fade-in');
    }

    // Template Methods
    async loadTemplates() {
        try {
            const response = await fetch('/api/templates');
            const result = await response.json();

            if (result.success) {
                this.populateTemplateSelect(result.data);
            } else {
                console.error('Failed to load templates:', result.message);
            }
        } catch (error) {
            console.error('Template load error:', error);
        }
    }

    populateTemplateSelect(templates) {
        const select = document.getElementById('template-select');

        // Clear existing options except first
        while (select.children.length > 1) {
            select.removeChild(select.lastChild);
        }

        // Add template options
        templates.forEach(template => {
            const option = document.createElement('option');
            option.value = template.filename;
            option.textContent = `${template.name} (${template.category})`;
            select.appendChild(option);
        });
    }

    async selectTemplate(filename) {
        if (!filename) {
            return;
        }

        if (!this.systemState.scan_data) {
            this.showMessage('Please scan visitor information first', 'warning');
            document.getElementById('template-select').value = '';
            return;
        }

        try {
            const response = await fetch('/api/template/load', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({
                    template: filename,
                    variables: this.systemState.scan_data
                })
            });

            const result = await response.json();

            if (result.success) {
                this.showMessage(`Template "${filename}" loaded successfully`, 'success');
            } else {
                this.showMessage(`Template load failed: ${result.message}`, 'danger');
                document.getElementById('template-select').value = '';
            }
        } catch (error) {
            console.error('Template selection error:', error);
            this.showMessage('Template selection error', 'danger');
            document.getElementById('template-select').value = '';
        }
    }

    // UI Update Methods
    updateUI() {
        this.updateConnectionStatus();
        this.updateButtonStates();
        this.updateProgress();
    }

    updateConnectionStatus() {
        const statusElement = document.getElementById('connection-status');
        const status = this.systemState.laser_status || 'disconnected';

        statusElement.className = 'badge';

        switch (status) {
            case 'connected':
            case 'idle':
                statusElement.classList.add('bg-success');
                statusElement.textContent = 'Connected';
                break;
            case 'marking':
                statusElement.classList.add('bg-warning');
                statusElement.textContent = 'Marking';
                break;
            case 'error':
                statusElement.classList.add('bg-danger');
                statusElement.textContent = 'Error';
                break;
            case 'door_open':
                statusElement.classList.add('bg-info');
                statusElement.textContent = 'Door Open';
                break;
            case 'door_closed':
                statusElement.classList.add('bg-info');
                statusElement.textContent = 'Door Closed';
                break;
            default:
                statusElement.classList.add('bg-secondary');
                statusElement.textContent = 'Disconnected';
        }
    }

    updateButtonStates() {
        const connected = this.systemState.laser_status &&
                         this.systemState.laser_status !== 'disconnected';
        const marking = this.systemState.laser_status === 'marking';
        const autoActive = this.systemState.auto_mode_active;
        const hasTemplate = !!this.systemState.current_template;
        const hasVisitorData = !!this.systemState.scan_data;

        // Connection buttons
        document.getElementById('connect-btn').disabled = connected;
        document.getElementById('disconnect-btn').disabled = !connected;

        // Operation buttons
        document.getElementById('auto-btn').disabled = !connected || !hasTemplate || !hasVisitorData || autoActive;
        document.getElementById('start-btn').disabled = !connected || marking || autoActive;
        document.getElementById('stop-btn').disabled = !connected || !marking;

        // Door buttons
        document.getElementById('close-door-btn').disabled = !connected || autoActive;
        document.getElementById('open-door-btn').disabled = !connected || autoActive;

        // Update AUTO button text based on state
        const autoBtn = document.getElementById('auto-btn');
        if (autoActive) {
            autoBtn.innerHTML = '<i class="bi bi-hourglass-split"></i> AUTO Running...';
            autoBtn.classList.remove('btn-primary');
            autoBtn.classList.add('btn-warning');
        } else {
            autoBtn.innerHTML = '<i class="bi bi-play-circle-fill"></i> AUTO';
            autoBtn.classList.remove('btn-warning');
            autoBtn.classList.add('btn-primary');
        }
    }

    updateProgress() {
        const progress = this.systemState.marking_progress || 0;
        const progressBar = document.getElementById('progress-bar');
        const progressText = document.getElementById('progress-text');

        progressBar.style.width = `${progress}%`;
        progressBar.textContent = `${progress}%`;

        if (progress === 0) {
            progressText.textContent = 'Ready';
            progressBar.className = 'progress-bar';
        } else if (progress < 100) {
            progressText.textContent = 'Processing...';
            progressBar.className = 'progress-bar bg-warning';
        } else {
            progressText.textContent = 'Complete';
            progressBar.className = 'progress-bar bg-success';
        }
    }

    // Utility Methods
    setButtonLoading(buttonId, loading) {
        const button = document.getElementById(buttonId);
        const originalText = button.innerHTML;

        if (loading) {
            button.disabled = true;
            button.innerHTML = '<span class="spinner-border spinner-border-sm me-2"></span>Loading...';
            button.dataset.originalText = originalText;
        } else {
            button.disabled = false;
            button.innerHTML = button.dataset.originalText || originalText;
        }
    }

    showMessage(message, type = 'info', duration = 5000) {
        const container = document.getElementById('status-messages');

        const alertDiv = document.createElement('div');
        alertDiv.className = `alert alert-${type} alert-dismissible fade show`;
        alertDiv.innerHTML = `
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;

        container.appendChild(alertDiv);

        // Auto-remove after duration
        if (duration > 0) {
            setTimeout(() => {
                if (alertDiv.parentNode) {
                    alertDiv.remove();
                }
            }, duration);
        }

        // Limit number of messages
        while (container.children.length > 5) {
            container.removeChild(container.firstChild);
        }
    }

    formatTimestamp(timestamp) {
        return new Date(timestamp * 1000).toLocaleString();
    }

    // Error handling
    handleError(error, context = '') {
        console.error(`Error in ${context}:`, error);
        this.showMessage(`Error: ${error.message || error}`, 'danger');
    }
}

// Initialize app when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    window.laserApp = new LaserDemoApp();
});

// Handle page visibility changes
document.addEventListener('visibilitychange', () => {
    if (document.hidden) {
        // Page is hidden - could pause some operations
        console.log('Page hidden');
    } else {
        // Page is visible - resume operations
        console.log('Page visible');
        if (window.laserApp && window.laserApp.socket) {
            window.laserApp.socket.emit('request_status');
        }
    }
});

// Handle beforeunload to cleanup camera
window.addEventListener('beforeunload', () => {
    if (window.laserApp && window.laserApp.cameraStream) {
        window.laserApp.stopCamera();
    }
});

// Service Worker registration for offline support (optional)
if ('serviceWorker' in navigator) {
    window.addEventListener('load', () => {
        navigator.serviceWorker.register('/static/js/sw.js')
            .then(registration => {
                console.log('SW registered: ', registration);
            })
            .catch(registrationError => {
                console.log('SW registration failed: ', registrationError);
            });
    });
}
