"""
Safety Controller for Laser System
Handles AUTO mode sequence and safety interlocks
"""

import time
import logging
import threading
from typing import Optional, Callable
from enum import Enum

from config import config
from ezcad3_client import EZcad3Client, LaserStatus

logger = logging.getLogger(__name__)

class AutoSequenceState(Enum):
    """AUTO mode sequence states"""
    IDLE = "idle"
    CLOSING_DOOR = "closing_door"
    DOOR_CLOSED = "door_closed"
    STARTING_MARK = "starting_mark"
    MARKING = "marking"
    MARK_COMPLETE = "mark_complete"
    OPENING_DOOR = "opening_door"
    COMPLETE = "complete"
    ERROR = "error"
    ABORTED = "aborted"

class SafetyController:
    """Controls laser safety systems and AUTO mode sequence"""
    
    def __init__(self, laser_client: EZcad3Client):
        self.laser_client = laser_client
        self.safety_door_timeout = config.laser.safety_door_timeout
        self.marking_timeout = config.laser.marking_timeout
        self.auto_mode_enabled = config.laser.auto_mode_enabled
        
        # State tracking
        self._auto_sequence_state = AutoSequenceState.IDLE
        self._auto_sequence_active = False
        self._emergency_stop_active = False
        self._abort_requested = False
        
        # Callbacks
        self._progress_callbacks: list[Callable] = []
        self._state_callbacks: list[Callable] = []
        
        # Safety monitoring
        self._safety_monitor_thread: Optional[threading.Thread] = None
        self._safety_monitor_running = False
        
        logger.info("SafetyController initialized")
    
    def add_progress_callback(self, callback: Callable[[int, str], None]):
        """Add callback for progress updates (progress%, message)"""
        self._progress_callbacks.append(callback)
    
    def add_state_callback(self, callback: Callable[[AutoSequenceState, dict], None]):
        """Add callback for state changes"""
        self._state_callbacks.append(callback)
    
    def _notify_progress(self, progress: int, message: str):
        """Notify progress callbacks"""
        for callback in self._progress_callbacks:
            try:
                callback(progress, message)
            except Exception as e:
                logger.error(f"Progress callback error: {e}")
    
    def _notify_state_change(self, state: AutoSequenceState, data: dict = None):
        """Notify state change callbacks"""
        self._auto_sequence_state = state
        for callback in self._state_callbacks:
            try:
                callback(state, data or {})
            except Exception as e:
                logger.error(f"State callback error: {e}")
    
    def execute_auto_sequence(self) -> bool:
        """
        Execute complete AUTO mode sequence:
        1. Close safety door
        2. Start marking
        3. Wait for completion
        4. Open safety door
        
        Returns:
            True if sequence completed successfully
        """
        if not self.auto_mode_enabled:
            logger.warning("AUTO mode is disabled")
            return False
        
        if self._auto_sequence_active:
            logger.warning("AUTO sequence already active")
            return False
        
        if not self.laser_client.connected:
            logger.error("Laser not connected")
            return False
        
        logger.info("Starting AUTO sequence")
        self._auto_sequence_active = True
        self._abort_requested = False
        self._emergency_stop_active = False
        
        try:
            # Step 1: Close safety door
            self._notify_state_change(AutoSequenceState.CLOSING_DOOR)
            self._notify_progress(10, "Closing safety door...")
            
            if not self._close_door_with_timeout():
                self._notify_state_change(AutoSequenceState.ERROR, {"error": "Failed to close door"})
                return False
            
            if self._abort_requested:
                self._notify_state_change(AutoSequenceState.ABORTED)
                return False
            
            # Step 2: Verify door closed
            self._notify_state_change(AutoSequenceState.DOOR_CLOSED)
            self._notify_progress(25, "Safety door closed")
            time.sleep(1)  # Brief pause for safety
            
            # Step 3: Start marking
            self._notify_state_change(AutoSequenceState.STARTING_MARK)
            self._notify_progress(30, "Starting laser marking...")
            
            response = self.laser_client.start_marking()
            if not response.success:
                self._notify_state_change(AutoSequenceState.ERROR, {"error": f"Failed to start marking: {response.message}"})
                return False
            
            if self._abort_requested:
                self.laser_client.stop_marking()
                self._notify_state_change(AutoSequenceState.ABORTED)
                return False
            
            # Step 4: Monitor marking progress
            self._notify_state_change(AutoSequenceState.MARKING)
            
            if not self._monitor_marking_progress():
                self._notify_state_change(AutoSequenceState.ERROR, {"error": "Marking failed or timed out"})
                return False
            
            if self._abort_requested:
                self.laser_client.stop_marking()
                self._notify_state_change(AutoSequenceState.ABORTED)
                return False
            
            # Step 5: Marking complete
            self._notify_state_change(AutoSequenceState.MARK_COMPLETE)
            self._notify_progress(80, "Marking completed")
            time.sleep(1)  # Brief pause
            
            # Step 6: Open safety door
            self._notify_state_change(AutoSequenceState.OPENING_DOOR)
            self._notify_progress(90, "Opening safety door...")
            
            if not self._open_door_with_timeout():
                self._notify_state_change(AutoSequenceState.ERROR, {"error": "Failed to open door"})
                return False
            
            # Step 7: Complete
            self._notify_state_change(AutoSequenceState.COMPLETE)
            self._notify_progress(100, "AUTO sequence completed successfully")
            
            logger.info("AUTO sequence completed successfully")
            return True
            
        except Exception as e:
            logger.error(f"AUTO sequence error: {e}")
            self._notify_state_change(AutoSequenceState.ERROR, {"error": str(e)})
            return False
        
        finally:
            self._auto_sequence_active = False
    
    def _close_door_with_timeout(self) -> bool:
        """Close door with timeout"""
        response = self.laser_client.close_door()
        if not response.success:
            logger.error(f"Door close command failed: {response.message}")
            return False
        
        # Wait for door to close (simulate - would check actual sensor)
        start_time = time.time()
        while time.time() - start_time < self.safety_door_timeout:
            if self._abort_requested or self._emergency_stop_active:
                return False
            
            # In real implementation, check door sensor status
            # For now, simulate door closing time
            time.sleep(0.5)
            
            # Simulate door closed after 2 seconds
            if time.time() - start_time > 2:
                logger.info("Safety door closed")
                return True
        
        logger.error("Door close timeout")
        return False
    
    def _open_door_with_timeout(self) -> bool:
        """Open door with timeout"""
        response = self.laser_client.open_door()
        if not response.success:
            logger.error(f"Door open command failed: {response.message}")
            return False
        
        # Wait for door to open (simulate - would check actual sensor)
        start_time = time.time()
        while time.time() - start_time < self.safety_door_timeout:
            if self._abort_requested or self._emergency_stop_active:
                return False
            
            # In real implementation, check door sensor status
            time.sleep(0.5)
            
            # Simulate door opened after 2 seconds
            if time.time() - start_time > 2:
                logger.info("Safety door opened")
                return True
        
        logger.error("Door open timeout")
        return False
    
    def _monitor_marking_progress(self) -> bool:
        """Monitor marking progress with timeout"""
        start_time = time.time()
        last_progress = 30
        
        while time.time() - start_time < self.marking_timeout:
            if self._abort_requested or self._emergency_stop_active:
                return False
            
            # Get progress from laser
            progress_response = self.laser_client.get_progress()
            
            if progress_response.success:
                # Parse progress (would be customized for actual EZcad3 responses)
                # For simulation, gradually increase progress
                elapsed = time.time() - start_time
                simulated_progress = min(30 + int((elapsed / 10) * 50), 80)  # 30% to 80% over 10 seconds
                
                if simulated_progress > last_progress:
                    last_progress = simulated_progress
                    self._notify_progress(simulated_progress, f"Marking in progress... {simulated_progress}%")
                
                # Check if marking is complete (would check actual status)
                if elapsed > 10:  # Simulate 10-second marking time
                    logger.info("Marking completed")
                    return True
            
            time.sleep(0.5)  # Check progress every 500ms
        
        logger.error("Marking timeout")
        return False
    
    def abort_auto_sequence(self) -> bool:
        """Abort current AUTO sequence"""
        if not self._auto_sequence_active:
            return True
        
        logger.warning("Aborting AUTO sequence")
        self._abort_requested = True
        
        # Stop marking if active
        if self._auto_sequence_state in [AutoSequenceState.MARKING, AutoSequenceState.STARTING_MARK]:
            self.laser_client.stop_marking()
        
        return True
    
    def emergency_stop(self) -> bool:
        """Activate emergency stop"""
        logger.critical("EMERGENCY STOP ACTIVATED")
        self._emergency_stop_active = True
        self._abort_requested = True
        
        # Send emergency stop to laser
        response = self.laser_client.emergency_stop()
        
        # Abort any active sequence
        if self._auto_sequence_active:
            self._notify_state_change(AutoSequenceState.ERROR, {"emergency_stop": True})
        
        return response.success
    
    def reset_emergency_stop(self) -> bool:
        """Reset emergency stop condition"""
        if not self._emergency_stop_active:
            return True
        
        logger.info("Resetting emergency stop")
        self._emergency_stop_active = False
        self._abort_requested = False
        
        return True
    
    def start_safety_monitoring(self):
        """Start safety monitoring thread"""
        if self._safety_monitor_thread and self._safety_monitor_thread.is_alive():
            return
        
        self._safety_monitor_running = True
        self._safety_monitor_thread = threading.Thread(target=self._safety_monitor_loop)
        self._safety_monitor_thread.daemon = True
        self._safety_monitor_thread.start()
        logger.info("Safety monitoring started")
    
    def stop_safety_monitoring(self):
        """Stop safety monitoring thread"""
        self._safety_monitor_running = False
        if self._safety_monitor_thread:
            self._safety_monitor_thread.join(timeout=5)
        logger.info("Safety monitoring stopped")
    
    def _safety_monitor_loop(self):
        """Safety monitoring loop"""
        while self._safety_monitor_running:
            try:
                # Monitor safety conditions
                # In real implementation, this would check:
                # - Door sensors
                # - Emergency stop buttons
                # - Laser interlocks
                # - Temperature sensors
                # - etc.
                
                time.sleep(1)  # Check every second
                
            except Exception as e:
                logger.error(f"Safety monitor error: {e}")
                time.sleep(5)
    
    @property
    def auto_sequence_active(self) -> bool:
        """Check if AUTO sequence is active"""
        return self._auto_sequence_active
    
    @property
    def auto_sequence_state(self) -> AutoSequenceState:
        """Get current AUTO sequence state"""
        return self._auto_sequence_state
    
    @property
    def emergency_stop_active(self) -> bool:
        """Check if emergency stop is active"""
        return self._emergency_stop_active
    
    def get_safety_status(self) -> dict:
        """Get comprehensive safety status"""
        return {
            'auto_sequence_active': self._auto_sequence_active,
            'auto_sequence_state': self._auto_sequence_state.value,
            'emergency_stop_active': self._emergency_stop_active,
            'abort_requested': self._abort_requested,
            'auto_mode_enabled': self.auto_mode_enabled,
            'safety_monitoring': self._safety_monitor_running
        }
