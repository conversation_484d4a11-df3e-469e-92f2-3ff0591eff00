"""
Barcode and QR Code Scanner Module
Supports both camera-based scanning and hardware barcode scanners
"""

import cv2
import json
import re
import time
import logging
from typing import Optional, Dict, Any, List
from pyzbar import pyzbar
from config import config

logger = logging.getLogger(__name__)

class BarcodeScanner:
    """Barcode and QR code scanner with multiple input methods"""
    
    def __init__(self):
        self.camera_index = config.scanner.camera_index
        self.scan_timeout = config.scanner.scan_timeout
        self.supported_formats = config.scanner.supported_formats
        
        # Camera instance
        self._camera = None
        
        logger.info("BarcodeScanner initialized")
    
    def scan_from_camera(self, timeout: int = None) -> Optional[str]:
        """
        Scan barcode/QR code from camera
        
        Args:
            timeout: Scan timeout in seconds (uses config default if None)
            
        Returns:
            Decoded string or None if no code found
        """
        timeout = timeout or self.scan_timeout
        
        try:
            # Initialize camera
            if not self._init_camera():
                logger.error("Failed to initialize camera")
                return None
            
            start_time = time.time()
            
            while time.time() - start_time < timeout:
                ret, frame = self._camera.read()
                if not ret:
                    logger.warning("Failed to read camera frame")
                    continue
                
                # Decode barcodes in frame
                barcodes = pyzbar.decode(frame)
                
                for barcode in barcodes:
                    # Check if format is supported
                    if barcode.type in self.supported_formats:
                        decoded_data = barcode.data.decode('utf-8')
                        logger.info(f"Scanned {barcode.type}: {decoded_data}")
                        return decoded_data
                
                # Small delay to prevent excessive CPU usage
                time.sleep(0.1)
            
            logger.info("Scan timeout reached")
            return None
            
        except Exception as e:
            logger.error(f"Camera scan error: {e}")
            return None
        finally:
            self._release_camera()
    
    def scan_from_image(self, image_path: str) -> Optional[str]:
        """
        Scan barcode/QR code from image file
        
        Args:
            image_path: Path to image file
            
        Returns:
            Decoded string or None if no code found
        """
        try:
            # Load image
            image = cv2.imread(image_path)
            if image is None:
                logger.error(f"Failed to load image: {image_path}")
                return None
            
            # Decode barcodes
            barcodes = pyzbar.decode(image)
            
            for barcode in barcodes:
                if barcode.type in self.supported_formats:
                    decoded_data = barcode.data.decode('utf-8')
                    logger.info(f"Scanned {barcode.type} from image: {decoded_data}")
                    return decoded_data
            
            logger.info("No supported barcodes found in image")
            return None
            
        except Exception as e:
            logger.error(f"Image scan error: {e}")
            return None
    
    def parse_scan_data(self, scan_data: str) -> Dict[str, Any]:
        """
        Parse scanned data and extract visitor information
        
        Args:
            scan_data: Raw scanned string
            
        Returns:
            Dictionary with parsed visitor information
        """
        parsed_data = {
            'raw_data': scan_data,
            'type': 'unknown',
            'name': '',
            'company': '',
            'email': '',
            'phone': '',
            'title': '',
            'timestamp': time.time()
        }
        
        try:
            # Try to parse as JSON (structured QR codes)
            if scan_data.startswith('{') and scan_data.endswith('}'):
                json_data = json.loads(scan_data)
                parsed_data.update(json_data)
                parsed_data['type'] = 'json'
                logger.info("Parsed JSON QR code")
                return parsed_data
            
            # Try to parse as vCard format
            if scan_data.startswith('BEGIN:VCARD'):
                return self._parse_vcard(scan_data, parsed_data)
            
            # Try to parse as URL with parameters
            if scan_data.startswith('http'):
                return self._parse_url(scan_data, parsed_data)
            
            # Try to parse as delimited text (e.g., "Name|Company|Email")
            if '|' in scan_data:
                return self._parse_delimited(scan_data, parsed_data, '|')
            
            # Try to parse as comma-separated values
            if ',' in scan_data:
                return self._parse_delimited(scan_data, parsed_data, ',')
            
            # Default: treat as simple text (name)
            parsed_data['name'] = scan_data.strip()
            parsed_data['type'] = 'text'
            
            logger.info(f"Parsed as simple text: {parsed_data['name']}")
            return parsed_data
            
        except Exception as e:
            logger.error(f"Parse error: {e}")
            parsed_data['name'] = scan_data.strip()
            parsed_data['type'] = 'text'
            return parsed_data
    
    def _parse_vcard(self, vcard_data: str, parsed_data: Dict) -> Dict[str, Any]:
        """Parse vCard format"""
        parsed_data['type'] = 'vcard'
        
        lines = vcard_data.split('\n')
        for line in lines:
            line = line.strip()
            
            if line.startswith('FN:'):
                parsed_data['name'] = line[3:]
            elif line.startswith('ORG:'):
                parsed_data['company'] = line[4:]
            elif line.startswith('EMAIL:'):
                parsed_data['email'] = line[6:]
            elif line.startswith('TEL:'):
                parsed_data['phone'] = line[4:]
            elif line.startswith('TITLE:'):
                parsed_data['title'] = line[6:]
        
        logger.info("Parsed vCard format")
        return parsed_data
    
    def _parse_url(self, url_data: str, parsed_data: Dict) -> Dict[str, Any]:
        """Parse URL with query parameters"""
        parsed_data['type'] = 'url'
        
        try:
            from urllib.parse import urlparse, parse_qs
            
            parsed_url = urlparse(url_data)
            params = parse_qs(parsed_url.query)
            
            # Extract common parameters
            if 'name' in params:
                parsed_data['name'] = params['name'][0]
            if 'company' in params:
                parsed_data['company'] = params['company'][0]
            if 'email' in params:
                parsed_data['email'] = params['email'][0]
            if 'phone' in params:
                parsed_data['phone'] = params['phone'][0]
            if 'title' in params:
                parsed_data['title'] = params['title'][0]
            
            logger.info("Parsed URL format")
            
        except Exception as e:
            logger.error(f"URL parse error: {e}")
        
        return parsed_data
    
    def _parse_delimited(self, data: str, parsed_data: Dict, delimiter: str) -> Dict[str, Any]:
        """Parse delimited text format"""
        parsed_data['type'] = 'delimited'
        
        parts = data.split(delimiter)
        
        # Assume order: Name, Company, Email, Phone, Title
        if len(parts) > 0:
            parsed_data['name'] = parts[0].strip()
        if len(parts) > 1:
            parsed_data['company'] = parts[1].strip()
        if len(parts) > 2:
            parsed_data['email'] = parts[2].strip()
        if len(parts) > 3:
            parsed_data['phone'] = parts[3].strip()
        if len(parts) > 4:
            parsed_data['title'] = parts[4].strip()
        
        logger.info(f"Parsed delimited format ({delimiter})")
        return parsed_data
    
    def _init_camera(self) -> bool:
        """Initialize camera for scanning"""
        try:
            if self._camera is not None:
                return True
            
            self._camera = cv2.VideoCapture(self.camera_index)
            if not self._camera.isOpened():
                logger.error(f"Failed to open camera {self.camera_index}")
                self._camera = None
                return False
            
            # Set camera properties for better scanning
            self._camera.set(cv2.CAP_PROP_FRAME_WIDTH, 640)
            self._camera.set(cv2.CAP_PROP_FRAME_HEIGHT, 480)
            self._camera.set(cv2.CAP_PROP_FPS, 30)
            
            logger.info(f"Camera {self.camera_index} initialized")
            return True
            
        except Exception as e:
            logger.error(f"Camera initialization error: {e}")
            return False
    
    def _release_camera(self):
        """Release camera resources"""
        if self._camera is not None:
            self._camera.release()
            self._camera = None
            logger.info("Camera released")
    
    def get_camera_frame(self) -> Optional[bytes]:
        """
        Get current camera frame as JPEG bytes for web streaming
        
        Returns:
            JPEG encoded frame or None
        """
        try:
            if not self._init_camera():
                return None
            
            ret, frame = self._camera.read()
            if not ret:
                return None
            
            # Encode frame as JPEG
            _, buffer = cv2.imencode('.jpg', frame)
            return buffer.tobytes()
            
        except Exception as e:
            logger.error(f"Frame capture error: {e}")
            return None
    
    def validate_scan_data(self, scan_data: str) -> bool:
        """
        Validate scanned data
        
        Args:
            scan_data: Scanned string to validate
            
        Returns:
            True if data appears valid
        """
        if not scan_data or len(scan_data.strip()) == 0:
            return False
        
        # Basic validation - can be extended
        if len(scan_data) > 1000:  # Reasonable length limit
            return False
        
        return True
    
    def create_sample_qr_data(self, name: str, company: str = "", email: str = "") -> str:
        """
        Create sample QR code data for testing
        
        Args:
            name: Visitor name
            company: Company name
            email: Email address
            
        Returns:
            JSON string for QR code generation
        """
        data = {
            'name': name,
            'company': company,
            'email': email,
            'timestamp': time.time(),
            'type': 'visitor_badge'
        }
        
        return json.dumps(data)
    
    def __del__(self):
        """Cleanup on destruction"""
        self._release_camera()
